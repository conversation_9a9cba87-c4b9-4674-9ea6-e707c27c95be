@model ManagementSystem.Controllers.DashboardViewModel
@{
    ViewData["Title"] = "لوحة التحكم";
    Layout = "_LayoutNew";
}

<!-- Welcome Section -->
<div class="bg-white overflow-hidden shadow rounded-lg mb-6">
    <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center">
                    <span class="text-white text-lg font-medium">@Model.CurrentUser.FullName.Substring(0, 1)</span>
                </div>
            </div>
            <div class="mr-4">
                <h1 class="text-lg font-medium text-gray-900">مرحباً، @Model.CurrentUser.FullName</h1>
                <p class="text-sm text-gray-500">@Model.CurrentUser.Position - @Model.CurrentUser.Department</p>
                @if (Model.CurrentUser.LastLoginAt.HasValue)
                {
                    <p class="text-xs text-gray-400">آخر تسجيل دخول: @Model.CurrentUser.LastLoginAt.Value.ToString("yyyy/MM/dd HH:mm")</p>
                }
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
    <!-- Total Users -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-users text-2xl text-blue-600"></i>
                </div>
                <div class="mr-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">إجمالي المستخدمين</dt>
                        <dd class="text-lg font-medium text-gray-900">@Model.TotalUsers</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <a href="@Url.Action("Index", "Users")" class="font-medium text-blue-700 hover:text-blue-900">
                    عرض التفاصيل
                    <i class="fas fa-arrow-left mr-1"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Active Users -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-user-check text-2xl text-green-600"></i>
                </div>
                <div class="mr-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">المستخدمين النشطين</dt>
                        <dd class="text-lg font-medium text-gray-900">@Model.ActiveUsers</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <span class="text-gray-600">
                    @(Model.TotalUsers > 0 ? Math.Round((double)Model.ActiveUsers / Model.TotalUsers * 100, 1) : 0)% من الإجمالي
                </span>
            </div>
        </div>
    </div>

    <!-- Total Roles -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-user-tag text-2xl text-purple-600"></i>
                </div>
                <div class="mr-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">الأدوار</dt>
                        <dd class="text-lg font-medium text-gray-900">@Model.TotalRoles</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <a href="@Url.Action("Index", "Roles")" class="font-medium text-purple-700 hover:text-purple-900">
                    إدارة الأدوار
                    <i class="fas fa-arrow-left mr-1"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Logins -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-sign-in-alt text-2xl text-orange-600"></i>
                </div>
                <div class="mr-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">تسجيلات دخول (7 أيام)</dt>
                        <dd class="text-lg font-medium text-gray-900">@Model.RecentLogins</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <span class="text-gray-600">
                    الأسبوع الماضي
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="bg-white shadow overflow-hidden sm:rounded-md mb-6">
    <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">الأنشطة الأخيرة</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">آخر العمليات المنفذة في النظام</p>
    </div>
    <ul class="divide-y divide-gray-200">
        @if (Model.RecentActivities.Any())
        {
            @foreach (var activity in Model.RecentActivities.Take(10))
            {
                <li class="px-4 py-4 sm:px-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                @{
                                    var iconClass = activity.Action switch
                                    {
                                        "Login" => "fas fa-sign-in-alt text-green-500",
                                        "Logout" => "fas fa-sign-out-alt text-gray-500",
                                        "CreateUser" => "fas fa-user-plus text-blue-500",
                                        "UpdateUser" => "fas fa-user-edit text-yellow-500",
                                        "DeleteUser" => "fas fa-user-minus text-red-500",
                                        _ => "fas fa-history text-gray-500"
                                    };
                                }
                                <i class="@iconClass"></i>
                            </div>
                            <div class="mr-4">
                                <div class="text-sm font-medium text-gray-900">
                                    @activity.UserName
                                </div>
                                <div class="text-sm text-gray-500">
                                    @{
                                        var actionText = activity.Action switch
                                        {
                                            "Login" => "سجل دخول",
                                            "Logout" => "سجل خروج",
                                            "CreateUser" => "أنشأ مستخدم جديد",
                                            "UpdateUser" => "عدل مستخدم",
                                            "DeleteUser" => "حذف مستخدم",
                                            "ChangePassword" => "غير كلمة المرور",
                                            _ => activity.Action
                                        };
                                    }
                                    @actionText
                                    @if (!string.IsNullOrEmpty(activity.EntityId))
                                    {
                                        <span class="text-gray-400">(@activity.EntityName: @activity.EntityId)</span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                            <time datetime="@activity.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss")">
                                @activity.CreatedAt.ToString("yyyy/MM/dd HH:mm")
                            </time>
                            @if (!string.IsNullOrEmpty(activity.IpAddress))
                            {
                                <span class="mr-2 text-xs text-gray-400">@activity.IpAddress</span>
                            }
                        </div>
                    </div>
                </li>
            }
        }
        else
        {
            <li class="px-4 py-4 sm:px-6">
                <div class="text-center text-gray-500">
                    <i class="fas fa-history text-4xl mb-2"></i>
                    <p>لا توجد أنشطة حديثة</p>
                </div>
            </li>
        }
    </ul>
    @if (Model.RecentActivities.Count() > 10)
    {
        <div class="bg-gray-50 px-4 py-3 sm:px-6">
            <div class="text-sm">
                <a href="@Url.Action("Index", "AuditLogs")" class="font-medium text-blue-700 hover:text-blue-900">
                    عرض جميع الأنشطة
                    <i class="fas fa-arrow-left mr-1"></i>
                </a>
            </div>
        </div>
    }
</div>

<!-- Quick Actions -->
<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">إجراءات سريعة</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">العمليات الأكثر استخداماً</p>
    </div>
    <div class="px-4 py-5 sm:p-6">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            @if (Model.UserPermissions.Contains("CreateUser"))
            {
                <a href="@Url.Action("Create", "Users")" class="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-blue-300">
                    <div>
                        <span class="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                            <i class="fas fa-user-plus text-xl"></i>
                        </span>
                    </div>
                    <div class="mt-4">
                        <h3 class="text-lg font-medium">
                            <span class="absolute inset-0" aria-hidden="true"></span>
                            إضافة مستخدم جديد
                        </h3>
                        <p class="mt-2 text-sm text-gray-500">
                            إنشاء حساب مستخدم جديد في النظام
                        </p>
                    </div>
                    <span class="pointer-events-none absolute top-6 left-6 text-gray-300 group-hover:text-gray-400" aria-hidden="true">
                        <i class="fas fa-arrow-left"></i>
                    </span>
                </a>
            }

            @if (Model.UserPermissions.Contains("ViewUsers"))
            {
                <a href="@Url.Action("Index", "Users")" class="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-blue-300">
                    <div>
                        <span class="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                            <i class="fas fa-users text-xl"></i>
                        </span>
                    </div>
                    <div class="mt-4">
                        <h3 class="text-lg font-medium">
                            <span class="absolute inset-0" aria-hidden="true"></span>
                            إدارة المستخدمين
                        </h3>
                        <p class="mt-2 text-sm text-gray-500">
                            عرض وإدارة جميع المستخدمين
                        </p>
                    </div>
                    <span class="pointer-events-none absolute top-6 left-6 text-gray-300 group-hover:text-gray-400" aria-hidden="true">
                        <i class="fas fa-arrow-left"></i>
                    </span>
                </a>
            }

            @if (Model.UserPermissions.Contains("ViewReports"))
            {
                <a href="@Url.Action("Index", "Reports")" class="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-blue-300">
                    <div>
                        <span class="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                            <i class="fas fa-chart-bar text-xl"></i>
                        </span>
                    </div>
                    <div class="mt-4">
                        <h3 class="text-lg font-medium">
                            <span class="absolute inset-0" aria-hidden="true"></span>
                            التقارير
                        </h3>
                        <p class="mt-2 text-sm text-gray-500">
                            عرض التقارير والإحصائيات
                        </p>
                    </div>
                    <span class="pointer-events-none absolute top-6 left-6 text-gray-300 group-hover:text-gray-400" aria-hidden="true">
                        <i class="fas fa-arrow-left"></i>
                    </span>
                </a>
            }
        </div>
    </div>
</div>

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ManagementSystem.Services;
using ManagementSystem.Models.ViewModels;

namespace ManagementSystem.Controllers
{
    public class UsersController : Controller
    {
        private readonly IUserService _userService;
        private readonly IAuthService _authService;
        private readonly ILogger<UsersController> _logger;

        public UsersController(
            IUserService userService,
            IAuthService authService,
            ILogger<UsersController> logger)
        {
            _userService = userService;
            _authService = authService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Index(
            int page = 1,
            int pageSize = 10,
            string? search = null,
            string? department = null,
            bool? isActive = null,
            string sortBy = "FirstName",
            string sortDirection = "asc")
        {
            try
            {
                // Demo mode - no authentication required

                var model = await _userService.GetUsersAsync(page, pageSize, search, department, isActive, sortBy, sortDirection);
                
                // Get departments for filter dropdown
                ViewBag.Departments = await _userService.GetDepartmentsAsync();

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading users list");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل قائمة المستخدمين";
                return View(new UserListViewModel());
            }
        }

        [HttpGet]
        public async Task<IActionResult> Details(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            try
            {
                var currentUser = await _authService.GetCurrentUserAsync();
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Auth");
                }

                // Check permission
                if (!await _authService.HasPermissionAsync(currentUser.Id, "ViewUsers"))
                {
                    return RedirectToAction("AccessDenied", "Auth");
                }

                var user = await _userService.GetUserDetailsAsync(id);
                if (user == null)
                {
                    return NotFound();
                }

                return View(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading user details for {UserId}", id);
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل تفاصيل المستخدم";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpGet]
        public async Task<IActionResult> Create()
        {
            try
            {
                var currentUser = await _authService.GetCurrentUserAsync();
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Auth");
                }

                // Check permission
                if (!await _authService.HasPermissionAsync(currentUser.Id, "CreateUser"))
                {
                    return RedirectToAction("AccessDenied", "Auth");
                }

                var model = new CreateUserViewModel
                {
                    AvailableRoles = await _userService.GetAvailableRolesAsync()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create user form");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل نموذج إنشاء المستخدم";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateUserViewModel model)
        {
            try
            {
                var currentUser = await _authService.GetCurrentUserAsync();
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Auth");
                }

                // Check permission
                if (!await _authService.HasPermissionAsync(currentUser.Id, "CreateUser"))
                {
                    return RedirectToAction("AccessDenied", "Auth");
                }

                if (!ModelState.IsValid)
                {
                    model.AvailableRoles = await _userService.GetAvailableRolesAsync();
                    return View(model);
                }

                var result = await _userService.CreateUserAsync(model, currentUser.Id);

                if (result.Succeeded)
                {
                    _logger.LogInformation("User {Email} created successfully by {CreatedBy}", model.Email, currentUser.Email);
                    TempData["SuccessMessage"] = "تم إنشاء المستخدم بنجاح";
                    return RedirectToAction(nameof(Index));
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }

                model.AvailableRoles = await _userService.GetAvailableRolesAsync();
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user {Email}", model.Email);
                ModelState.AddModelError(string.Empty, "حدث خطأ أثناء إنشاء المستخدم");
                model.AvailableRoles = await _userService.GetAvailableRolesAsync();
                return View(model);
            }
        }

        [HttpGet]
        public async Task<IActionResult> Edit(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            try
            {
                var currentUser = await _authService.GetCurrentUserAsync();
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Auth");
                }

                // Check permission
                if (!await _authService.HasPermissionAsync(currentUser.Id, "EditUser"))
                {
                    return RedirectToAction("AccessDenied", "Auth");
                }

                var userDetails = await _userService.GetUserDetailsAsync(id);
                if (userDetails == null)
                {
                    return NotFound();
                }

                var model = new EditUserViewModel
                {
                    Id = userDetails.Id,
                    FirstName = userDetails.FirstName,
                    LastName = userDetails.LastName,
                    Email = userDetails.Email,
                    PhoneNumber = userDetails.PhoneNumber,
                    Department = userDetails.Department,
                    Position = userDetails.Position,
                    IsActive = userDetails.IsActive,
                    SelectedRoles = userDetails.Roles.Select(r => r.Name).ToList(),
                    AvailableRoles = await _userService.GetAvailableRolesAsync()
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit user form for {UserId}", id);
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل نموذج تعديل المستخدم";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(EditUserViewModel model)
        {
            try
            {
                var currentUser = await _authService.GetCurrentUserAsync();
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Auth");
                }

                // Check permission
                if (!await _authService.HasPermissionAsync(currentUser.Id, "EditUser"))
                {
                    return RedirectToAction("AccessDenied", "Auth");
                }

                if (!ModelState.IsValid)
                {
                    model.AvailableRoles = await _userService.GetAvailableRolesAsync();
                    return View(model);
                }

                var result = await _userService.UpdateUserAsync(model, currentUser.Id);

                if (result.Succeeded)
                {
                    _logger.LogInformation("User {UserId} updated successfully by {UpdatedBy}", model.Id, currentUser.Email);
                    TempData["SuccessMessage"] = "تم تحديث المستخدم بنجاح";
                    return RedirectToAction(nameof(Details), new { id = model.Id });
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }

                model.AvailableRoles = await _userService.GetAvailableRolesAsync();
                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId}", model.Id);
                ModelState.AddModelError(string.Empty, "حدث خطأ أثناء تحديث المستخدم");
                model.AvailableRoles = await _userService.GetAvailableRolesAsync();
                return View(model);
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            try
            {
                var currentUser = await _authService.GetCurrentUserAsync();
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Auth");
                }

                // Check permission
                if (!await _authService.HasPermissionAsync(currentUser.Id, "DeleteUser"))
                {
                    return RedirectToAction("AccessDenied", "Auth");
                }

                // Prevent self-deletion
                if (id == currentUser.Id)
                {
                    TempData["ErrorMessage"] = "لا يمكنك حذف حسابك الخاص";
                    return RedirectToAction(nameof(Index));
                }

                var result = await _userService.DeleteUserAsync(id, currentUser.Id);

                if (result.Succeeded)
                {
                    _logger.LogInformation("User {UserId} deleted successfully by {DeletedBy}", id, currentUser.Email);
                    TempData["SuccessMessage"] = "تم حذف المستخدم بنجاح";
                }
                else
                {
                    TempData["ErrorMessage"] = "حدث خطأ أثناء حذف المستخدم";
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {UserId}", id);
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف المستخدم";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ToggleStatus(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            try
            {
                var currentUser = await _authService.GetCurrentUserAsync();
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Auth");
                }

                // Check permission
                if (!await _authService.HasPermissionAsync(currentUser.Id, "EditUser"))
                {
                    return RedirectToAction("AccessDenied", "Auth");
                }

                // Prevent self-deactivation
                if (id == currentUser.Id)
                {
                    TempData["ErrorMessage"] = "لا يمكنك تعطيل حسابك الخاص";
                    return RedirectToAction(nameof(Index));
                }

                var result = await _userService.ToggleUserStatusAsync(id, currentUser.Id);

                if (result.Succeeded)
                {
                    _logger.LogInformation("User {UserId} status toggled successfully by {UpdatedBy}", id, currentUser.Email);
                    TempData["SuccessMessage"] = "تم تحديث حالة المستخدم بنجاح";
                }
                else
                {
                    TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث حالة المستخدم";
                }

                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling user status for {UserId}", id);
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث حالة المستخدم";
                return RedirectToAction(nameof(Index));
            }
        }
    }
}

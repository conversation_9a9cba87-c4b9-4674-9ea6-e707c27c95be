/* أنميشن هبوط جذاب للمحتوى الرئيسي */
.animate-drop {
    opacity: 0;
    transform: translateY(-60px) scale(0.98);
    filter: blur(6px);
    animation: dropDownIn 1.1s cubic-bezier(0.23, 1, 0.32, 1) forwards;
}
@keyframes dropDownIn {
    0% {
        opacity: 0;
        transform: translateY(-60px) scale(0.98);
        filter: blur(6px);
    }
    60% {
        opacity: 1;
        transform: translateY(10px) scale(1.01);
        filter: blur(0px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0px);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    document.querySelector('main').classList.add('animate-drop');
});
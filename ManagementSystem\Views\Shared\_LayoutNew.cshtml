<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - نظام الإدارة</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Tajawal', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts for Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .sidebar-transition { transition: all 0.3s ease; }
    </style>
</head>
<body class="bg-gray-50 font-arabic">
    @if (User.Identity?.IsAuthenticated == true)
    {
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-0 transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0">
            <div class="flex items-center justify-center h-16 bg-blue-600">
                <h1 class="text-white text-xl font-bold">نظام الإدارة</h1>
            </div>
            
            <nav class="mt-5 px-2">
                <a href="@Url.Action("Index", "Dashboard")" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 mb-1">
                    <i class="fas fa-tachometer-alt ml-3 text-gray-400 group-hover:text-gray-500"></i>
                    لوحة التحكم
                </a>
                
                <a href="@Url.Action("Index", "Users")" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 mb-1">
                    <i class="fas fa-users ml-3 text-gray-400 group-hover:text-gray-500"></i>
                    إدارة المستخدمين
                </a>
                
                <a href="@Url.Action("Index", "Roles")" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 mb-1">
                    <i class="fas fa-user-tag ml-3 text-gray-400 group-hover:text-gray-500"></i>
                    إدارة الأدوار
                </a>
                
                <a href="@Url.Action("Index", "AuditLogs")" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 mb-1">
                    <i class="fas fa-history ml-3 text-gray-400 group-hover:text-gray-500"></i>
                    سجل العمليات
                </a>
                
                <a href="@Url.Action("Index", "Reports")" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 mb-1">
                    <i class="fas fa-chart-bar ml-3 text-gray-400 group-hover:text-gray-500"></i>
                    التقارير
                </a>
                
                <a href="@Url.Action("Index", "Settings")" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 mb-1">
                    <i class="fas fa-cog ml-3 text-gray-400 group-hover:text-gray-500"></i>
                    الإعدادات
                </a>
            </nav>
        </div>

        <!-- Main content -->
        <div class="lg:pr-64 flex flex-col flex-1">
            <!-- Top navigation -->
            <div class="sticky top-0 z-10 flex-shrink-0 flex h-16 bg-white shadow">
                <button type="button" class="px-4 border-l border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 lg:hidden" onclick="toggleSidebar()">
                    <span class="sr-only">فتح الشريط الجانبي</span>
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="flex-1 px-4 flex justify-between">
                    <div class="flex-1 flex">
                        <!-- Search can be added here -->
                    </div>
                    <div class="mr-4 flex items-center lg:mr-6">
                        <!-- Profile dropdown -->
                        <div class="mr-3 relative">
                            <div class="flex items-center">
                                <button type="button" class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" onclick="toggleProfileMenu()">
                                    <span class="sr-only">فتح قائمة المستخدم</span>
                                    <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                                        <span class="text-white text-sm font-medium">@User.Identity?.Name?.Substring(0, 1).ToUpper()</span>
                                    </div>
                                </button>
                            </div>
                            
                            <div id="profileMenu" class="hidden origin-top-left absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                                <div class="py-1">
                                    <a href="@Url.Action("Profile", "Account")" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">الملف الشخصي</a>
                                    <a href="@Url.Action("ChangePassword", "Auth")" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">تغيير كلمة المرور</a>
                                    <form asp-controller="Auth" asp-action="Logout" method="post" class="block">
                                        <button type="submit" class="w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">تسجيل الخروج</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <main class="flex-1">
                <!-- Page header -->
                @if (ViewData["Title"] != null)
                {
                    <div class="bg-white shadow">
                        <div class="px-4 sm:px-6 lg:px-8">
                            <div class="py-6">
                                <h1 class="text-2xl font-bold text-gray-900">@ViewData["Title"]</h1>
                            </div>
                        </div>
                    </div>
                }

                <!-- Alerts -->
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="mx-4 mt-4 sm:mx-6 lg:mx-8">
                        <div class="bg-green-50 border border-green-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-green-400"></i>
                                </div>
                                <div class="mr-3">
                                    <p class="text-sm font-medium text-green-800">@TempData["SuccessMessage"]</p>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                @if (TempData["ErrorMessage"] != null)
                {
                    <div class="mx-4 mt-4 sm:mx-6 lg:mx-8">
                        <div class="bg-red-50 border border-red-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle text-red-400"></i>
                                </div>
                                <div class="mr-3">
                                    <p class="text-sm font-medium text-red-800">@TempData["ErrorMessage"]</p>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                @if (TempData["InfoMessage"] != null)
                {
                    <div class="mx-4 mt-4 sm:mx-6 lg:mx-8">
                        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-blue-400"></i>
                                </div>
                                <div class="mr-3">
                                    <p class="text-sm font-medium text-blue-800">@TempData["InfoMessage"]</p>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                <!-- Page content -->
                <div class="py-6">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        @RenderBody()
                    </div>
                </div>
            </main>
        </div>
    }
    else
    {
        <!-- Login layout -->
        <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            @RenderBody()
        </div>
    }

    <!-- Scripts -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('-translate-x-full');
        }

        function toggleProfileMenu() {
            const menu = document.getElementById('profileMenu');
            menu.classList.toggle('hidden');
        }

        // Close profile menu when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('profileMenu');
            const button = event.target.closest('button');
            
            if (!button || !button.onclick || button.onclick.toString().indexOf('toggleProfileMenu') === -1) {
                menu.classList.add('hidden');
            }
        });
    </script>
    
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ManagementSystem.Services;
using ManagementSystem.Models.ViewModels;

namespace ManagementSystem.Controllers
{
    public class AuthController : Controller
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IAuthService authService, ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult Login(string? returnUrl = null)
        {
            if (User.Identity?.IsAuthenticated == true)
            {
                return RedirectToAction("Index", "Dashboard");
            }

            ViewData["ReturnUrl"] = returnUrl;
            return View(new LoginViewModel { ReturnUrl = returnUrl });
        }

        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var result = await _authService.LoginAsync(model);

                if (result.Succeeded)
                {
                    _logger.LogInformation("User {Email} logged in successfully", model.Email);
                    
                    if (!string.IsNullOrEmpty(model.ReturnUrl) && Url.IsLocalUrl(model.ReturnUrl))
                    {
                        return Redirect(model.ReturnUrl);
                    }
                    
                    return RedirectToAction("Index", "Dashboard");
                }

                if (result.IsLockedOut)
                {
                    ModelState.AddModelError(string.Empty, "تم قفل حسابك مؤقتاً بسبب محاولات تسجيل دخول خاطئة متعددة");
                    _logger.LogWarning("User {Email} account locked out", model.Email);
                }
                else if (result.RequiresTwoFactor)
                {
                    // Handle two-factor authentication if implemented
                    ModelState.AddModelError(string.Empty, "يتطلب التحقق بخطوتين");
                }
                else
                {
                    ModelState.AddModelError(string.Empty, "البريد الإلكتروني أو كلمة المرور غير صحيحة");
                    _logger.LogWarning("Failed login attempt for {Email}", model.Email);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for {Email}", model.Email);
                ModelState.AddModelError(string.Empty, "حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى");
            }

            return View(model);
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult Register()
        {
            if (User.Identity?.IsAuthenticated == true)
            {
                return RedirectToAction("Index", "Dashboard");
            }

            return View(new RegisterViewModel());
        }

        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Register(RegisterViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var result = await _authService.RegisterAsync(model);

                if (result.Succeeded)
                {
                    _logger.LogInformation("User {Email} registered successfully", model.Email);
                    TempData["SuccessMessage"] = "تم إنشاء الحساب بنجاح. يمكنك الآن تسجيل الدخول";
                    return RedirectToAction(nameof(Login));
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, GetLocalizedError(error.Description));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration for {Email}", model.Email);
                ModelState.AddModelError(string.Empty, "حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى");
            }

            return View(model);
        }

        [HttpPost]
        [Authorize]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout()
        {
            try
            {
                await _authService.LogoutAsync();
                _logger.LogInformation("User logged out");
                TempData["InfoMessage"] = "تم تسجيل الخروج بنجاح";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
            }

            return RedirectToAction(nameof(Login));
        }

        [HttpGet]
        [Authorize]
        public IActionResult ChangePassword()
        {
            return View(new ChangePasswordViewModel());
        }

        [HttpPost]
        [Authorize]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ChangePassword(ChangePasswordViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var user = await _authService.GetCurrentUserAsync();
                if (user == null)
                {
                    return RedirectToAction(nameof(Login));
                }

                var result = await _authService.ChangePasswordAsync(user.Id, model);

                if (result.Succeeded)
                {
                    _logger.LogInformation("User {UserId} changed password successfully", user.Id);
                    TempData["SuccessMessage"] = "تم تغيير كلمة المرور بنجاح";
                    return RedirectToAction("Profile", "Account");
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, GetLocalizedError(error.Description));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password change");
                ModelState.AddModelError(string.Empty, "حدث خطأ أثناء تغيير كلمة المرور. يرجى المحاولة مرة أخرى");
            }

            return View(model);
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult ForgotPassword()
        {
            return View(new ForgotPasswordViewModel());
        }

        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ForgotPassword(ForgotPasswordViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var token = await _authService.GeneratePasswordResetTokenAsync(model.Email);
                
                if (!string.IsNullOrEmpty(token))
                {
                    // In a real application, you would send an email with the reset link
                    // For now, we'll just show a success message
                    _logger.LogInformation("Password reset token generated for {Email}", model.Email);
                    TempData["InfoMessage"] = "إذا كان البريد الإلكتروني موجود في النظام، فسيتم إرسال رابط إعادة تعيين كلمة المرور";
                }
                else
                {
                    // Don't reveal that the user doesn't exist
                    TempData["InfoMessage"] = "إذا كان البريد الإلكتروني موجود في النظام، فسيتم إرسال رابط إعادة تعيين كلمة المرور";
                }

                return RedirectToAction(nameof(Login));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during forgot password for {Email}", model.Email);
                ModelState.AddModelError(string.Empty, "حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى");
            }

            return View(model);
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult ResetPassword(string? code = null, string? email = null)
        {
            if (string.IsNullOrEmpty(code) || string.IsNullOrEmpty(email))
            {
                return RedirectToAction(nameof(Login));
            }

            return View(new ResetPasswordViewModel { Code = code, Email = email });
        }

        [HttpPost]
        [AllowAnonymous]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ResetPassword(ResetPasswordViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var result = await _authService.ResetPasswordAsync(model);

                if (result.Succeeded)
                {
                    _logger.LogInformation("Password reset successfully for {Email}", model.Email);
                    TempData["SuccessMessage"] = "تم إعادة تعيين كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول";
                    return RedirectToAction(nameof(Login));
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, GetLocalizedError(error.Description));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password reset for {Email}", model.Email);
                ModelState.AddModelError(string.Empty, "حدث خطأ أثناء إعادة تعيين كلمة المرور. يرجى المحاولة مرة أخرى");
            }

            return View(model);
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult AccessDenied()
        {
            return View();
        }

        private string GetLocalizedError(string error)
        {
            // Simple error message localization
            return error switch
            {
                var e when e.Contains("Password") && e.Contains("digit") => "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل",
                var e when e.Contains("Password") && e.Contains("uppercase") => "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل",
                var e when e.Contains("Password") && e.Contains("lowercase") => "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل",
                var e when e.Contains("Password") && e.Contains("special") => "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل",
                var e when e.Contains("Email") && e.Contains("taken") => "البريد الإلكتروني مستخدم بالفعل",
                var e when e.Contains("Username") && e.Contains("taken") => "اسم المستخدم مستخدم بالفعل",
                _ => error
            };
        }
    }
}

@model ManagementSystem.Models.ViewModels.CreateUserViewModel
@{
    ViewData["Title"] = "إضافة مستخدم جديد";
    Layout = "_LayoutNew";
}

<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">إضافة مستخدم جديد</h1>
                <p class="mt-1 text-sm text-gray-600">املأ النموذج أدناه لإنشاء حساب مستخدم جديد</p>
            </div>
            <a href="@Url.Action("Index", "Users")" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة إلى القائمة
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white shadow rounded-lg">
        <form asp-action="Create" method="post" class="space-y-6 p-6">
            <div asp-validation-summary="ModelOnly" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-400"></i>
                    </div>
                    <div class="mr-3">
                        <h3 class="text-sm font-medium text-red-800">يرجى تصحيح الأخطاء التالية:</h3>
                    </div>
                </div>
            </div>

            <!-- Personal Information -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">المعلومات الشخصية</h3>
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label asp-for="FirstName" class="block text-sm font-medium text-gray-700">الاسم الأول *</label>
                        <input asp-for="FirstName" type="text" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" 
                               placeholder="أدخل الاسم الأول">
                        <span asp-validation-for="FirstName" class="text-red-600 text-sm"></span>
                    </div>

                    <div>
                        <label asp-for="LastName" class="block text-sm font-medium text-gray-700">الاسم الأخير *</label>
                        <input asp-for="LastName" type="text" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" 
                               placeholder="أدخل الاسم الأخير">
                        <span asp-validation-for="LastName" class="text-red-600 text-sm"></span>
                    </div>

                    <div>
                        <label asp-for="Email" class="block text-sm font-medium text-gray-700">البريد الإلكتروني *</label>
                        <input asp-for="Email" type="email" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" 
                               placeholder="<EMAIL>">
                        <span asp-validation-for="Email" class="text-red-600 text-sm"></span>
                    </div>

                    <div>
                        <label asp-for="PhoneNumber" class="block text-sm font-medium text-gray-700">رقم الهاتف</label>
                        <input asp-for="PhoneNumber" type="tel" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" 
                               placeholder="+966 50 123 4567">
                        <span asp-validation-for="PhoneNumber" class="text-red-600 text-sm"></span>
                    </div>
                </div>
            </div>

            <!-- Work Information -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">معلومات العمل</h3>
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label asp-for="Department" class="block text-sm font-medium text-gray-700">القسم</label>
                        <input asp-for="Department" type="text" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" 
                               placeholder="مثل: تقنية المعلومات">
                        <span asp-validation-for="Department" class="text-red-600 text-sm"></span>
                    </div>

                    <div>
                        <label asp-for="Position" class="block text-sm font-medium text-gray-700">المنصب</label>
                        <input asp-for="Position" type="text" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" 
                               placeholder="مثل: مطور برمجيات">
                        <span asp-validation-for="Position" class="text-red-600 text-sm"></span>
                    </div>
                </div>
            </div>

            <!-- Account Settings -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">إعدادات الحساب</h3>
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label asp-for="Password" class="block text-sm font-medium text-gray-700">كلمة المرور *</label>
                        <input asp-for="Password" type="password" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" 
                               placeholder="أدخل كلمة مرور قوية">
                        <span asp-validation-for="Password" class="text-red-600 text-sm"></span>
                        <p class="mt-1 text-xs text-gray-500">يجب أن تحتوي على 6 أحرف على الأقل مع أحرف كبيرة وصغيرة ورقم</p>
                    </div>

                    <div>
                        <label asp-for="ConfirmPassword" class="block text-sm font-medium text-gray-700">تأكيد كلمة المرور *</label>
                        <input asp-for="ConfirmPassword" type="password" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" 
                               placeholder="أعد إدخال كلمة المرور">
                        <span asp-validation-for="ConfirmPassword" class="text-red-600 text-sm"></span>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="flex items-center">
                        <input asp-for="IsActive" type="checkbox" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label asp-for="IsActive" class="mr-2 block text-sm text-gray-900">
                            تفعيل الحساب
                        </label>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">إذا لم يتم تحديد هذا الخيار، لن يتمكن المستخدم من تسجيل الدخول</p>
                </div>
            </div>

            <!-- Roles -->
            <div class="pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">الأدوار والصلاحيات</h3>
                <div class="space-y-3">
                    @if (Model.AvailableRoles.Any())
                    {
                        @foreach (var role in Model.AvailableRoles)
                        {
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input type="checkbox" name="SelectedRoles" value="@role.Name" 
                                           @(Model.SelectedRoles.Contains(role.Name) ? "checked" : "")
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                </div>
                                <div class="mr-3 text-sm">
                                    <label class="font-medium text-gray-700">@role.Name</label>
                                    @if (!string.IsNullOrEmpty(role.Description))
                                    {
                                        <p class="text-gray-500">@role.Description</p>
                                    }
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <p class="text-gray-500 text-sm">لا توجد أدوار متاحة</p>
                    }
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-3 space-x-reverse pt-6 border-t border-gray-200">
                <a href="@Url.Action("Index", "Users")" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    إلغاء
                </a>
                <button type="submit" 
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-save ml-2"></i>
                    إنشاء المستخدم
                </button>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        // Auto-generate username from email
        document.getElementById('Email').addEventListener('input', function() {
            // Additional client-side validation can be added here
        });

        // Password strength indicator
        document.getElementById('Password').addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);
            // You can add a password strength indicator here
        });

        function calculatePasswordStrength(password) {
            let strength = 0;
            if (password.length >= 6) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            return strength;
        }
    </script>
}

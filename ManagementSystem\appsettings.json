{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=ManagementSystemDb;Trusted_Connection=true;MultipleActiveResultSets=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "logs/log-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 10485760, "rollOnFileSizeLimit": true}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "EmailSettings": {"Enabled": false, "SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "EnableSsl": true, "Username": "<EMAIL>", "Password": "your-app-password", "FromEmail": "<EMAIL>", "FromName": "نظام الإدارة"}, "AllowedHosts": "*"}
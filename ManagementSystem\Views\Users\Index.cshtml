@model ManagementSystem.Models.ViewModels.UserListViewModel
@{
    ViewData["Title"] = "إدارة المستخدمين";
    Layout = "_LayoutNew";
}

<!-- Page Header -->
<div class="sm:flex sm:items-center sm:justify-between mb-6">
    <div class="sm:flex-auto">
        <h1 class="text-xl font-semibold text-gray-900">المستخدمين</h1>
        <p class="mt-2 text-sm text-gray-700">قائمة بجميع المستخدمين في النظام مع إمكانية البحث والتصفية</p>
    </div>
    <div class="mt-4 sm:mt-0 sm:mr-16 sm:flex-none">
        <a href="@Url.Action("Create", "Users")" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto">
            <i class="fas fa-plus ml-2"></i>
            إضافة مستخدم جديد
        </a>
    </div>
</div>

<!-- Filters -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-4 py-5 sm:p-6">
        <form method="get" class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700">البحث</label>
                <input type="text" name="search" id="search" value="@Model.SearchTerm" 
                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm" 
                       placeholder="الاسم أو البريد الإلكتروني">
            </div>
            
            <div>
                <label for="department" class="block text-sm font-medium text-gray-700">القسم</label>
                <select name="department" id="department" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    <option value="">جميع الأقسام</option>
                    @foreach (var dept in ViewBag.Departments as IEnumerable<string> ?? new List<string>())
                    {
                        <option value="@dept" @(Model.Department == dept ? "selected" : "")>@dept</option>
                    }
                </select>
            </div>
            
            <div>
                <label for="isActive" class="block text-sm font-medium text-gray-700">الحالة</label>
                <select name="isActive" id="isActive" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    <option value="">جميع الحالات</option>
                    <option value="true" @(Model.IsActive == true ? "selected" : "")>نشط</option>
                    <option value="false" @(Model.IsActive == false ? "selected" : "")>غير نشط</option>
                </select>
            </div>
            
            <div class="flex items-end">
                <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent bg-blue-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    <i class="fas fa-search ml-2"></i>
                    بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="bg-white shadow overflow-hidden sm:rounded-md">
    <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                النتائج (@Model.TotalCount مستخدم)
            </h3>
            <div class="flex items-center space-x-2 space-x-reverse">
                <label for="pageSize" class="text-sm text-gray-700">عرض:</label>
                <select name="pageSize" id="pageSize" onchange="changePageSize(this.value)" 
                        class="rounded-md border-gray-300 text-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="10" @(Model.PageSize == 10 ? "selected" : "")>10</option>
                    <option value="25" @(Model.PageSize == 25 ? "selected" : "")>25</option>
                    <option value="50" @(Model.PageSize == 50 ? "selected" : "")>50</option>
                </select>
            </div>
        </div>
    </div>
    
    @if (Model.Users.Any())
    {
        <ul class="divide-y divide-gray-200">
            @foreach (var user in Model.Users)
            {
                <li class="px-4 py-4 sm:px-6 hover:bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-10 w-10 rounded-full @(user.IsActive ? "bg-green-500" : "bg-gray-400") flex items-center justify-center">
                                    <span class="text-white text-sm font-medium">@user.FirstName.Substring(0, 1)@user.LastName.Substring(0, 1)</span>
                                </div>
                            </div>
                            <div class="mr-4 min-w-0 flex-1">
                                <div class="flex items-center">
                                    <p class="text-sm font-medium text-gray-900 truncate">
                                        @user.FullName
                                    </p>
                                    @if (!user.IsActive)
                                    {
                                        <span class="mr-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            غير نشط
                                        </span>
                                    }
                                </div>
                                <div class="flex items-center mt-1">
                                    <p class="text-sm text-gray-500 truncate">@user.Email</p>
                                    @if (!string.IsNullOrEmpty(user.Department))
                                    {
                                        <span class="mr-2 text-sm text-gray-400">•</span>
                                        <p class="text-sm text-gray-500">@user.Department</p>
                                    }
                                </div>
                                @if (user.Roles.Any())
                                {
                                    <div class="mt-1">
                                        @foreach (var role in user.Roles)
                                        {
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 ml-1">
                                                @role
                                            </span>
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <div class="text-sm text-gray-500 text-left">
                                <p>انضم: @user.CreatedAt.ToString("yyyy/MM/dd")</p>
                                @if (user.LastLoginAt.HasValue)
                                {
                                    <p>آخر دخول: @user.LastLoginAt.Value.ToString("yyyy/MM/dd")</p>
                                }
                                else
                                {
                                    <p class="text-gray-400">لم يسجل دخول</p>
                                }
                            </div>
                            <div class="flex items-center space-x-1 space-x-reverse">
                                <a href="@Url.Action("Details", "Users", new { id = user.Id })" 
                                   class="text-blue-600 hover:text-blue-900 p-1" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="@Url.Action("Edit", "Users", new { id = user.Id })" 
                                   class="text-yellow-600 hover:text-yellow-900 p-1" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form asp-action="ToggleStatus" asp-route-id="@user.Id" method="post" class="inline">
                                    <button type="submit" 
                                            class="@(user.IsActive ? "text-orange-600 hover:text-orange-900" : "text-green-600 hover:text-green-900") p-1" 
                                            title="@(user.IsActive ? "تعطيل" : "تفعيل")"
                                            onclick="return confirm('@(user.IsActive ? "هل أنت متأكد من تعطيل هذا المستخدم؟" : "هل أنت متأكد من تفعيل هذا المستخدم؟")')">
                                        <i class="fas @(user.IsActive ? "fa-user-slash" : "fa-user-check")"></i>
                                    </button>
                                </form>
                                <form asp-action="Delete" asp-route-id="@user.Id" method="post" class="inline">
                                    <button type="submit" 
                                            class="text-red-600 hover:text-red-900 p-1" 
                                            title="حذف"
                                            onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </li>
            }
        </ul>
        
        <!-- Pagination -->
        @if (Model.TotalPages > 1)
        {
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    @if (Model.HasPreviousPage)
                    {
                        <a href="@Url.Action("Index", new { page = Model.PageNumber - 1, pageSize = Model.PageSize, search = Model.SearchTerm, department = Model.Department, isActive = Model.IsActive })" 
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            السابق
                        </a>
                    }
                    @if (Model.HasNextPage)
                    {
                        <a href="@Url.Action("Index", new { page = Model.PageNumber + 1, pageSize = Model.PageSize, search = Model.SearchTerm, department = Model.Department, isActive = Model.IsActive })" 
                           class="mr-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            التالي
                        </a>
                    }
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            عرض
                            <span class="font-medium">@((Model.PageNumber - 1) * Model.PageSize + 1)</span>
                            إلى
                            <span class="font-medium">@Math.Min(Model.PageNumber * Model.PageSize, Model.TotalCount)</span>
                            من
                            <span class="font-medium">@Model.TotalCount</span>
                            نتيجة
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            @if (Model.HasPreviousPage)
                            {
                                <a href="@Url.Action("Index", new { page = Model.PageNumber - 1, pageSize = Model.PageSize, search = Model.SearchTerm, department = Model.Department, isActive = Model.IsActive })" 
                                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            }
                            
                            @for (int i = Math.Max(1, Model.PageNumber - 2); i <= Math.Min(Model.TotalPages, Model.PageNumber + 2); i++)
                            {
                                @if (i == Model.PageNumber)
                                {
                                    <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                                        @i
                                    </span>
                                }
                                else
                                {
                                    <a href="@Url.Action("Index", new { page = i, pageSize = Model.PageSize, search = Model.SearchTerm, department = Model.Department, isActive = Model.IsActive })" 
                                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        @i
                                    </a>
                                }
                            }
                            
                            @if (Model.HasNextPage)
                            {
                                <a href="@Url.Action("Index", new { page = Model.PageNumber + 1, pageSize = Model.PageSize, search = Model.SearchTerm, department = Model.Department, isActive = Model.IsActive })" 
                                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            }
                        </nav>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="text-center py-12">
            <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
            <h3 class="mt-2 text-sm font-medium text-gray-900">لا توجد مستخدمين</h3>
            <p class="mt-1 text-sm text-gray-500">ابدأ بإضافة مستخدم جديد.</p>
            <div class="mt-6">
                <a href="@Url.Action("Create", "Users")" 
                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة مستخدم جديد
                </a>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script>
        function changePageSize(pageSize) {
            const url = new URL(window.location);
            url.searchParams.set('pageSize', pageSize);
            url.searchParams.set('page', '1');
            window.location = url;
        }
    </script>
}

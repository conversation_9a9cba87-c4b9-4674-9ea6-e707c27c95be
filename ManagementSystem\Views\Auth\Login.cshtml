@model ManagementSystem.Models.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "تسجيل الدخول";
    Layout = "_LayoutNew";
}

<div class="max-w-md w-full space-y-8">
    <div>
        <div class="mx-auto h-12 w-auto flex justify-center">
            <i class="fas fa-user-shield text-4xl text-blue-600"></i>
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            تسجيل الدخول إلى حسابك
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
            أو
            <a href="@Url.Action("Register", "Auth")" class="font-medium text-blue-600 hover:text-blue-500">
                إنشاء حساب جديد
            </a>
        </p>
    </div>
    
    <form class="mt-8 space-y-6" asp-action="Login" method="post">
        <input type="hidden" name="ReturnUrl" value="@Model.ReturnUrl" />
        <div asp-validation-summary="ModelOnly" class="text-red-600 text-sm"></div>
        
        <div class="rounded-md shadow-sm -space-y-px">
            <div>
                <label asp-for="Email" class="sr-only">اسم المستخدم</label>
                <input asp-for="Email" type="text" autocomplete="username" required
                       class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                       placeholder="اسم المستخدم" />
                <span asp-validation-for="Email" class="text-red-600 text-sm"></span>
            </div>
            <div>
                <label asp-for="Password" class="sr-only">كلمة المرور</label>
                <input asp-for="Password" type="password" autocomplete="current-password" required 
                       class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm" 
                       placeholder="كلمة المرور" />
                <span asp-validation-for="Password" class="text-red-600 text-sm"></span>
            </div>
        </div>

        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <input asp-for="RememberMe" type="checkbox" 
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
                <label asp-for="RememberMe" class="mr-2 block text-sm text-gray-900">
                    تذكرني
                </label>
            </div>

            <div class="text-sm">
                <a href="@Url.Action("ForgotPassword", "Auth")" class="font-medium text-blue-600 hover:text-blue-500">
                    نسيت كلمة المرور؟
                </a>
            </div>
        </div>

        <div>
            <button type="submit" 
                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <span class="absolute right-0 inset-y-0 flex items-center pr-3">
                    <i class="fas fa-sign-in-alt text-blue-500 group-hover:text-blue-400"></i>
                </span>
                تسجيل الدخول
            </button>
        </div>
    </form>
    
    <!-- Demo credentials info -->
    <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
        <h3 class="text-sm font-medium text-blue-800 mb-2">بيانات تسجيل الدخول:</h3>
        <div class="text-xs text-blue-700">
            <p><strong>اسم المستخدم:</strong> admin</p>
            <p><strong>كلمة المرور:</strong> 123</p>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ManagementSystem.Services;
using ManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace ManagementSystem.Controllers
{
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IAuthService _authService;
        private readonly IAuditService _auditService;
        private readonly ILogger<DashboardController> _logger;

        public DashboardController(
            ApplicationDbContext context,
            IAuthService authService,
            IAuditService auditService,
            ILogger<DashboardController> logger)
        {
            _context = context;
            _authService = authService;
            _auditService = auditService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var currentUser = await _authService.GetCurrentUserAsync();
                if (currentUser == null)
                {
                    return RedirectToAction("Login", "Auth");
                }

                // Get dashboard statistics
                var totalUsers = await _context.Users.CountAsync();
                var activeUsers = await _context.Users.CountAsync(u => u.IsActive);
                var totalRoles = await _context.Roles.CountAsync();
                var recentLogins = await _context.Users
                    .Where(u => u.LastLoginAt.HasValue && u.LastLoginAt.Value >= DateTime.UtcNow.AddDays(-7))
                    .CountAsync();

                // Get recent activities
                var recentActivities = await _auditService.GetRecentActivitiesAsync(count: 10);

                // Get user permissions
                var userPermissions = await _authService.GetUserPermissionsAsync(currentUser.Id);

                var dashboardData = new DashboardViewModel
                {
                    TotalUsers = totalUsers,
                    ActiveUsers = activeUsers,
                    InactiveUsers = totalUsers - activeUsers,
                    TotalRoles = totalRoles,
                    RecentLogins = recentLogins,
                    RecentActivities = recentActivities,
                    UserPermissions = userPermissions,
                    CurrentUser = new UserSummaryViewModel
                    {
                        Id = currentUser.Id,
                        FullName = $"{currentUser.FirstName} {currentUser.LastName}",
                        Email = currentUser.Email,
                        Department = currentUser.Department,
                        Position = currentUser.Position,
                        LastLoginAt = currentUser.LastLoginAt
                    }
                };

                return View(dashboardData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dashboard");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل لوحة التحكم";
                return View(new DashboardViewModel());
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetChartData(string chartType)
        {
            try
            {
                switch (chartType.ToLower())
                {
                    case "usersbydepartment":
                        var usersByDepartment = await _context.Users
                            .Where(u => !string.IsNullOrEmpty(u.Department))
                            .GroupBy(u => u.Department)
                            .Select(g => new { Department = g.Key, Count = g.Count() })
                            .ToListAsync();
                        return Json(usersByDepartment);

                    case "usersbymonth":
                        var usersByMonth = await _context.Users
                            .Where(u => u.CreatedAt >= DateTime.UtcNow.AddMonths(-12))
                            .GroupBy(u => new { u.CreatedAt.Year, u.CreatedAt.Month })
                            .Select(g => new { 
                                Month = $"{g.Key.Year}-{g.Key.Month:D2}", 
                                Count = g.Count() 
                            })
                            .OrderBy(x => x.Month)
                            .ToListAsync();
                        return Json(usersByMonth);

                    case "activitiesbytype":
                        var activitiesByType = await _context.AuditLogs
                            .Where(a => a.CreatedAt >= DateTime.UtcNow.AddDays(-30))
                            .GroupBy(a => a.Action)
                            .Select(g => new { Action = g.Key, Count = g.Count() })
                            .OrderByDescending(x => x.Count)
                            .Take(10)
                            .ToListAsync();
                        return Json(activitiesByType);

                    default:
                        return BadRequest("نوع الرسم البياني غير مدعوم");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chart data for {ChartType}", chartType);
                return StatusCode(500, "حدث خطأ أثناء تحميل البيانات");
            }
        }
    }

    public class DashboardViewModel
    {
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int InactiveUsers { get; set; }
        public int TotalRoles { get; set; }
        public int RecentLogins { get; set; }
        public IEnumerable<ManagementSystem.Models.ViewModels.AuditLogItemViewModel> RecentActivities { get; set; } = new List<ManagementSystem.Models.ViewModels.AuditLogItemViewModel>();
        public IEnumerable<string> UserPermissions { get; set; } = new List<string>();
        public UserSummaryViewModel CurrentUser { get; set; } = new UserSummaryViewModel();
    }

    public class UserSummaryViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Department { get; set; }
        public string? Position { get; set; }
        public DateTime? LastLoginAt { get; set; }
    }
}

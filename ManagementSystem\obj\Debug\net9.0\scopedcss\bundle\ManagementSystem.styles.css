/* _content/ManagementSystem/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-k76ghj5gli] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-k76ghj5gli] {
  color: #0077cc;
}

.btn-primary[b-k76ghj5gli] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-k76ghj5gli], .nav-pills .show > .nav-link[b-k76ghj5gli] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-k76ghj5gli] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-k76ghj5gli] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-k76ghj5gli] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-k76ghj5gli] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-k76ghj5gli] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}

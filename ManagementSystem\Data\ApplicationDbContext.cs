using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using ManagementSystem.Models;

namespace ManagementSystem.Data
{
    public class ApplicationDbContext : IdentityDbContext<User, Role, string, 
        Microsoft.AspNetCore.Identity.IdentityUserClaim<string>,
        UserRole,
        Microsoft.AspNetCore.Identity.IdentityUserLogin<string>,
        Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>,
        Microsoft.AspNetCore.Identity.IdentityUserToken<string>>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Permission> Permissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure User entity
            builder.Entity<User>(entity =>
            {
                entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Department).HasMaxLength(200);
                entity.Property(e => e.Position).HasMaxLength(200);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            });

            // Configure Role entity
            builder.Entity<Role>(entity =>
            {
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            });

            // Configure UserRole relationship
            builder.Entity<UserRole>(entity =>
            {
                entity.HasKey(ur => new { ur.UserId, ur.RoleId });
                
                entity.HasOne(ur => ur.User)
                    .WithMany(u => u.UserRoles)
                    .HasForeignKey(ur => ur.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(ur => ur.Role)
                    .WithMany(r => r.UserRoles)
                    .HasForeignKey(ur => ur.RoleId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure Permission entity
            builder.Entity<Permission>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.Module).IsRequired().HasMaxLength(100);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
                
                entity.HasIndex(e => new { e.Name, e.Module }).IsUnique();
            });

            // Configure RolePermission relationship
            builder.Entity<RolePermission>(entity =>
            {
                entity.HasKey(rp => new { rp.RoleId, rp.PermissionId });

                entity.HasOne(rp => rp.Role)
                    .WithMany(r => r.RolePermissions)
                    .HasForeignKey(rp => rp.RoleId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(rp => rp.Permission)
                    .WithMany(p => p.RolePermissions)
                    .HasForeignKey(rp => rp.PermissionId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure AuditLog entity
            builder.Entity<AuditLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Action).IsRequired().HasMaxLength(100);
                entity.Property(e => e.EntityName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.IpAddress).HasMaxLength(45);
                entity.Property(e => e.UserAgent).HasMaxLength(500);

                entity.HasOne(e => e.User)
                    .WithMany(u => u.AuditLogs)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => new { e.EntityName, e.EntityId });
            });

            // Seed default permissions
            SeedPermissions(builder);
        }

        private void SeedPermissions(ModelBuilder builder)
        {
            var permissions = new[]
            {
                new Permission { Id = 1, Name = "ViewUsers", Description = "عرض المستخدمين", Module = "UserManagement" },
                new Permission { Id = 2, Name = "CreateUser", Description = "إنشاء مستخدم جديد", Module = "UserManagement" },
                new Permission { Id = 3, Name = "EditUser", Description = "تعديل المستخدم", Module = "UserManagement" },
                new Permission { Id = 4, Name = "DeleteUser", Description = "حذف المستخدم", Module = "UserManagement" },
                new Permission { Id = 5, Name = "ViewRoles", Description = "عرض الأدوار", Module = "RoleManagement" },
                new Permission { Id = 6, Name = "CreateRole", Description = "إنشاء دور جديد", Module = "RoleManagement" },
                new Permission { Id = 7, Name = "EditRole", Description = "تعديل الدور", Module = "RoleManagement" },
                new Permission { Id = 8, Name = "DeleteRole", Description = "حذف الدور", Module = "RoleManagement" },
                new Permission { Id = 9, Name = "ViewAuditLogs", Description = "عرض سجل العمليات", Module = "AuditManagement" },
                new Permission { Id = 10, Name = "ViewReports", Description = "عرض التقارير", Module = "Reports" },
                new Permission { Id = 11, Name = "SystemSettings", Description = "إعدادات النظام", Module = "Settings" }
            };

            builder.Entity<Permission>().HasData(permissions);
        }
    }
}

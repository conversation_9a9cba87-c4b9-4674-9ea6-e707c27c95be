using ManagementSystem.Models;
using ManagementSystem.Models.ViewModels;
using Microsoft.AspNetCore.Identity;

namespace ManagementSystem.Services
{
    public interface IAuthService
    {
        Task<SignInResult> LoginAsync(LoginViewModel model);
        Task<IdentityResult> RegisterAsync(RegisterViewModel model);
        Task LogoutAsync();
        Task<IdentityResult> ChangePasswordAsync(string userId, ChangePasswordViewModel model);
        Task<string> GeneratePasswordResetTokenAsync(string email);
        Task<IdentityResult> ResetPasswordAsync(ResetPasswordViewModel model);
        Task<User?> GetCurrentUserAsync();
        Task<bool> HasPermissionAsync(string userId, string permission);
        Task<IEnumerable<string>> GetUserPermissionsAsync(string userId);
        Task UpdateLastLoginAsync(string userId);
    }

    public class AuthService : IAuthService
    {
        private readonly UserManager<User> _userManager;
        private readonly SignInManager<User> _signInManager;
        private readonly RoleManager<Role> _roleManager;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IAuditService _auditService;

        public AuthService(
            UserManager<User> userManager,
            SignInManager<User> signInManager,
            RoleManager<Role> roleManager,
            IHttpContextAccessor httpContextAccessor,
            IAuditService auditService)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _roleManager = roleManager;
            _httpContextAccessor = httpContextAccessor;
            _auditService = auditService;
        }

        public async Task<SignInResult> LoginAsync(LoginViewModel model)
        {
            // Try to find user by username first, then by email
            var user = await _userManager.FindByNameAsync(model.Email);
            if (user == null)
            {
                user = await _userManager.FindByEmailAsync(model.Email);
            }

            if (user == null || !user.IsActive)
            {
                return SignInResult.Failed;
            }

            var result = await _signInManager.PasswordSignInAsync(
                user, model.Password, model.RememberMe, lockoutOnFailure: true);

            if (result.Succeeded)
            {
                await UpdateLastLoginAsync(user.Id);
                await _auditService.LogAsync("Login", "User", user.Id, null, null, user.Id);
            }
            else
            {
                await _auditService.LogAsync("LoginFailed", "User", user.Id, null, null, user.Id);
            }

            return result;
        }

        public async Task<IdentityResult> RegisterAsync(RegisterViewModel model)
        {
            var user = new User
            {
                UserName = model.Email,
                Email = model.Email,
                FirstName = model.FirstName,
                LastName = model.LastName,
                Department = model.Department,
                Position = model.Position,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            var result = await _userManager.CreateAsync(user, model.Password);

            if (result.Succeeded)
            {
                await _auditService.LogAsync("Register", "User", user.Id, null, 
                    $"{{\"Email\":\"{user.Email}\",\"FirstName\":\"{user.FirstName}\",\"LastName\":\"{user.LastName}\"}}", 
                    user.Id);
            }

            return result;
        }

        public async Task LogoutAsync()
        {
            var user = await GetCurrentUserAsync();
            if (user != null)
            {
                await _auditService.LogAsync("Logout", "User", user.Id, null, null, user.Id);
            }
            
            await _signInManager.SignOutAsync();
        }

        public async Task<IdentityResult> ChangePasswordAsync(string userId, ChangePasswordViewModel model)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return IdentityResult.Failed(new IdentityError { Description = "المستخدم غير موجود" });
            }

            var result = await _userManager.ChangePasswordAsync(user, model.CurrentPassword, model.NewPassword);

            if (result.Succeeded)
            {
                await _auditService.LogAsync("ChangePassword", "User", user.Id, null, null, userId);
            }

            return result;
        }

        public async Task<string> GeneratePasswordResetTokenAsync(string email)
        {
            var user = await _userManager.FindByEmailAsync(email);
            if (user == null || !user.IsActive)
            {
                return string.Empty;
            }

            return await _userManager.GeneratePasswordResetTokenAsync(user);
        }

        public async Task<IdentityResult> ResetPasswordAsync(ResetPasswordViewModel model)
        {
            var user = await _userManager.FindByEmailAsync(model.Email);
            if (user == null)
            {
                return IdentityResult.Failed(new IdentityError { Description = "المستخدم غير موجود" });
            }

            var result = await _userManager.ResetPasswordAsync(user, model.Code, model.Password);

            if (result.Succeeded)
            {
                await _auditService.LogAsync("ResetPassword", "User", user.Id, null, null, user.Id);
            }

            return result;
        }

        public async Task<User?> GetCurrentUserAsync()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated == true)
            {
                return await _userManager.GetUserAsync(httpContext.User);
            }
            return null;
        }

        public async Task<bool> HasPermissionAsync(string userId, string permission)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null || !user.IsActive)
            {
                return false;
            }

            var userRoles = await _userManager.GetRolesAsync(user);
            
            // Check if user has the permission through any of their roles
            foreach (var roleName in userRoles)
            {
                var role = await _roleManager.FindByNameAsync(roleName);
                if (role != null && role.IsActive)
                {
                    // This would need to be implemented with a proper permission check
                    // For now, we'll assume admin role has all permissions
                    if (roleName.Equals("Admin", StringComparison.OrdinalIgnoreCase))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public async Task<IEnumerable<string>> GetUserPermissionsAsync(string userId)
        {
            var permissions = new List<string>();
            var user = await _userManager.FindByIdAsync(userId);
            
            if (user == null || !user.IsActive)
            {
                return permissions;
            }

            var userRoles = await _userManager.GetRolesAsync(user);
            
            // This would need to be implemented with proper permission lookup
            // For now, we'll return basic permissions based on roles
            foreach (var roleName in userRoles)
            {
                if (roleName.Equals("Admin", StringComparison.OrdinalIgnoreCase))
                {
                    permissions.AddRange(new[] { "ViewUsers", "CreateUser", "EditUser", "DeleteUser", 
                        "ViewRoles", "CreateRole", "EditRole", "DeleteRole", "ViewAuditLogs", 
                        "ViewReports", "SystemSettings" });
                }
                else if (roleName.Equals("Manager", StringComparison.OrdinalIgnoreCase))
                {
                    permissions.AddRange(new[] { "ViewUsers", "CreateUser", "EditUser", 
                        "ViewRoles", "ViewReports" });
                }
                else if (roleName.Equals("User", StringComparison.OrdinalIgnoreCase))
                {
                    permissions.AddRange(new[] { "ViewUsers" });
                }
            }

            return permissions.Distinct();
        }

        public async Task UpdateLastLoginAsync(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user != null)
            {
                user.LastLoginAt = DateTime.UtcNow;
                await _userManager.UpdateAsync(user);
            }
        }
    }
}

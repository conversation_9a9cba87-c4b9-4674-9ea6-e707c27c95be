{"version": "0.2.0", "configurations": [{"name": "Launch Management System", "type": "dotnet", "request": "launch", "projectPath": "${workspaceFolder}/ManagementSystem.csproj", "program": "${workspaceFolder}/bin/Debug/net9.0/ManagementSystem.dll", "args": [], "cwd": "${workspaceFolder}", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Attach to Management System", "type": "dotnet", "request": "attach"}]}
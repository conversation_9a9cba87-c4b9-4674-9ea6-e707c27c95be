DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام إدارة الموظفين - شركة فور جي لتوصيل الطلبات</title>
  
  <!-- Meta Tags -->
  <meta name="description" content="نظام متكامل لإدارة الموظفين والسائقين والرواتب وأسطول المركبات - شركة فور جي">
  <meta name="keywords" content="موظفين, سائقين, رواتب, توصيل طلبات, فور جي, الكويت">
  <meta name="author" content="MOHAMED T">
  
  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
  
  <style>
    body {
      font-family: 'Cairo', sans-serif;
      background: linear-gradient(135deg, #1f0b10 0%, #3b1e29 50%, #55333e 100%);
      min-height: 100vh;
    }
    .glass-card {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
    }
    .pro-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(15px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .pro-card:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(244, 63, 94, 0.5);
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 30px 60px rgba(244, 63, 94, 0.2);
    }
    .pro-btn {
      background: linear-gradient(135deg, #f43f5e 0%, #be123c 100%);
      border: none;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    .pro-btn:hover {
      background: linear-gradient(135deg, #e11d48 0%, #9f1239 100%);
      transform: translateY(-2px);
      box-shadow: 0 15px 35px rgba(225, 29, 72, 0.4);
    }
    .pro-btn-outline {
      background: transparent;
      border: 2px solid rgba(255, 255, 255, 0.3);
      color: white;
      transition: all 0.3s ease;
    }
    .pro-btn-outline:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(244, 63, 94, 0.8);
      color: #f43f5e;
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(244, 63, 94, 0.3);
    }
    .animate-scale-in {
      animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
      opacity: 0;
      transform: scale(0.9) translateY(20px);
    }
    @keyframes scaleIn {
      to {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }
    .floating-animation {
      animation: floating 6s ease-in-out infinite;
    }
    @keyframes floating {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }
    .gradient-text {
      background: linear-gradient(135deg, #f43f5e, #ec4899, #ef4444);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    .stats-card {
      background: rgba(255, 255, 255, 0.08);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.15);
      transition: all 0.3s ease;
      cursor: pointer;
    }
    .stats-card:hover {
      background: rgba(255, 255, 255, 0.12);
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }
    .notification {
      position: fixed;
      top: 20px;
      left: 20px;
      right: 20px;
      z-index: 1050;
      transform: translateY(-120px);
      transition: transform 0.4s ease-in-out;
    }
    .notification.show {
      transform: translateY(0);
    }
    .modal {
        position: fixed;
        inset: 0;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(10px);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s, visibility 0.3s;
    }
    .modal.show {
        opacity: 1;
        visibility: visible;
    }
    .modal-content {
      background: rgba(25, 12, 18, 0.95);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      max-height: 90vh;
      overflow-y: auto;
      width: 100%;
    }
    .form-input {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      transition: all 0.3s ease;
    }
    .form-input:focus {
      background: rgba(255, 255, 255, 0.15);
      border-color: #f43f5e;
      box-shadow: 0 0 0 3px rgba(244, 63, 94, 0.1);
    }
    .status-badge { padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: 600; }
    .status-active { background: rgba(16, 185, 129, 0.2); color: #10b981; border: 1px solid rgba(16, 185, 129, 0.3); }
    .status-on_leave { background: rgba(245, 158, 11, 0.2); color: #f59e0b; border: 1px solid rgba(245, 158, 11, 0.3); }
    .status-inactive { background: rgba(100, 116, 139, 0.2); color: #94a3b8; border: 1px solid rgba(100, 116, 139, 0.3); }
    .category-badge { padding: 4px 12px; border-radius: 20px; font-size: 0.875rem; font-weight: 600; }
    .job-driver { background: rgba(249, 115, 22, 0.2); color: #f97316; border: 1px solid rgba(249, 115, 22, 0.3); }
    .job-supervisor { background: rgba(245, 158, 11, 0.2); color: #f59e0b; border: 1px solid rgba(245, 158, 11, 0.3); }
    .job-admin { background: rgba(16, 185, 129, 0.2); color: #10b981; border: 1px solid rgba(16, 185, 129, 0.3); }
    
    .expiry-soon { background: rgba(245, 158, 11, 0.2); color: #f59e0b; border: 1px solid rgba(245, 158, 11, 0.3); }
    .expiry-expired { background: rgba(239, 68, 68, 0.2); color: #ef4444; border: 1px solid rgba(239, 68, 68, 0.3); }
    .expiry-valid { background: rgba(16, 185, 129, 0.2); color: #10b981; border: 1px solid rgba(16, 185, 129, 0.3); }

    /* Print styles for Payslip */
    @media print {
      body * { visibility: hidden; }
      #employeeProfileContent, #employeeProfileContent * { visibility: visible; }
      #employeeProfileContent {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 20px;
        border: none;
        box-shadow: none;
        background: white !important;
        color: black !important;
      }
      #employeeProfileContent h3, #employeeProfileContent h4, #employeeProfileContent p, #employeeProfileContent span, #employeeProfileContent td, #employeeProfileContent div {
        color: black !important;
      }
      .no-print { display: none; }
    }

    /* Light Theme */
    .light-theme {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%) !important;
    }
    .light-theme .glass-card {
      background: rgba(255, 255, 255, 0.9) !important;
      border: 1px solid rgba(0, 0, 0, 0.1) !important;
      color: #1e293b !important;
    }
    .light-theme .pro-card {
      background: rgba(255, 255, 255, 0.8) !important;
      border: 1px solid rgba(0, 0, 0, 0.1) !important;
      color: #1e293b !important;
    }
    .light-theme .text-white { color: #1e293b !important; }
    .light-theme .text-slate-300 { color: #475569 !important; }
    .light-theme .text-pink-300 { color: #f472b6 !important; }
    .light-theme .form-input {
      background: rgba(255, 255, 255, 0.9) !important;
      border: 1px solid rgba(0, 0, 0, 0.2) !important;
      color: #1e293b !important;
    }
    .light-theme .modal {
      background: rgba(255, 255, 255, 0.8) !important;
    }
    .light-theme .modal-content {
      background: rgba(255, 255, 255, 0.95) !important;
      color: #1e293b !important;
    }
    .neon-text {
      color: #fff;
      text-shadow: 
        0 0 5px #f43f5e,
        0 0 10px #f43f5e,
        0 0 15px #f43f5e,
        0 0 20px #f43f5e,
        0 0 35px #f43f5e,
        0 0 40px #f43f5e;
      animation: neonGlow 2s ease-in-out infinite alternate;
    }

    @keyframes neonGlow {
      from {
        text-shadow: 
          0 0 5px #f43f5e,
          0 0 10px #f43f5e,
          0 0 15px #f43f5e,
          0 0 20px #f43f5e,
          0 0 35px #f43f5e,
          0 0 40px #f43f5e;
      }
      to {
        text-shadow: 
          0 0 2px #f43f5e,
          0 0 5px #f43f5e,
          0 0 8px #f43f5e,
          0 0 12px #f43f5e,
          0 0 18px #f43f5e,
          0 0 25px #f43f5e;
      }
    }

    .neon-border {
      border: 2px solid #f43f5e;
      box-shadow: 
        0 0 5px #f43f5e,
        0 0 10px #f43f5e,
        0 0 15px #f43f5e,
        inset 0 0 5px #f43f5e;
      animation: neonBorderGlow 3s ease-in-out infinite alternate;
    }

    @keyframes neonBorderGlow {
      from {
        box-shadow: 
          0 0 5px #f43f5e,
          0 0 10px #f43f5e,
          0 0 15px #f43f5e,
          inset 0 0 5px #f43f5e;
      }
      to {
        box-shadow: 
          0 0 10px #f43f5e,
          0 0 20px #f43f5e,
          0 0 30px #f43f5e,
          inset 0 0 10px #f43f5e;
      }
    }
    .neon-text {
      color: #fff;
      text-shadow: 
        0 0 5px #f43f5e,
        0 0 10px #f43f5e,
        0 0 15px #f43f5e,
        0 0 20px #f43f5e,
        0 0 35px #f43f5e,
        0 0 40px #f43f5e;
      animation: neonGlow 2s ease-in-out infinite alternate;
    }

    @keyframes neonGlow {
      from {
        text-shadow: 
          0 0 5px #f43f5e,
          0 0 10px #f43f5e,
          0 0 15px #f43f5e,
          0 0 20px #f43f5e,
          0 0 35px #f43f5e,
          0 0 40px #f43f5e;
      }
      to {
        text-shadow: 
          0 0 2px #f43f5e,
          0 0 5px #f43f5e,
          0 0 8px #f43f5e,
          0 0 12px #f43f5e,
          0 0 18px #f43f5e,
          0 0 25px #f43f5e;
      }
    }

    .neon-border {
      border: 2px solid #f43f5e;
      box-shadow: 
        0 0 5px #f43f5e,
        0 0 10px #f43f5e,
        0 0 15px #f43f5e,
        inset 0 0 5px #f43f5e;
      animation: neonBorderGlow 3s ease-in-out infinite alternate;
    }

    @keyframes neonBorderGlow {
      from {
        box-shadow: 
          0 0 5px #f43f5e,
          0 0 10px #f43f5e,
          0 0 15px #f43f5e,
          inset 0 0 5px #f43f5e;
      }
      to {
        box-shadow: 
          0 0 10px #f43f5e,
          0 0 20px #f43f5e,
          0 0 30px #f43f5e,
          inset 0 0 10px #f43f5e;
      }
    }
  </style>
</head>
<body class="min-h-screen">
  <!-- Header -->
  <header class="glass-card rounded-none border-x-0 border-t-0 mb-8">
    <div class="container mx-auto px-6 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="w-16 h-16 rounded-2xl flex items-center justify-center floating-animation">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjUwIiBjeT0iNTAiIHI9IjQ1IiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgo8dGV4dCB4PSI1MCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIzNiIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj40RzwvdGV4dD4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgo8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjQzZjVlO3N0b3Atb3BhY2l0eToxIiAvPgo8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNlYzQ4OTk7c3RvcC1vcGFjaXR5OjEiIC8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+" alt="شعار شركة فور جي" class="w-full h-full object-contain rounded-2xl">
          </div>
          <div>
            <h1 class="text-3xl font-bold gradient-text">نظام إدارة الموظفين المتكامل</h1>
            <p class="text-pink-300 text-lg">شركة فور جي لتوصيل الطلبات</p>
          </div>
        </div>
        <div class="flex items-center space-x-4 space-x-reverse">
          <button onclick="toggleTheme()" class="pro-btn-outline px-4 py-2 rounded-lg text-sm">
            <i id="themeIcon" class="fas fa-moon"></i>
            <span id="themeText">الوضع المظلم</span>
          </button>
          <div class="text-center">
            <div class="text-sm text-pink-300">التاريخ الحالي</div>
            <div id="currentDate" class="text-white font-semibold"></div>
          </div>
          <div class="text-center">
            <div class="text-sm text-pink-300">الوقت الحالي</div>
            <div id="currentTime" class="text-white font-semibold"></div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div class="container mx-auto px-6">
    <!-- Welcome Section -->
    <div class="glass-card rounded-3xl p-8 mb-12 text-center animate-scale-in">
      <div class="mb-6">
        <h2 class="text-4xl font-bold text-white mb-4 neon-text">أهلاً بك في نظام فور جي للموظفين</h2>
        <p class="text-xl text-slate-300 leading-relaxed">نظام شامل لإدارة شؤون الموظفين، الرواتب، أسطول المركبات، وعمليات التوصيل</p>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        <div class="stats-card rounded-2xl p-6" onclick="showEmployeesSection()"><div class="text-3xl font-bold text-rose-400 mb-2" id="totalEmployees">0</div><div class="text-slate-300">إجمالي الموظفين</div></div>
        <div class="stats-card rounded-2xl p-6" onclick="showFleetSection()"><div class="text-3xl font-bold text-orange-400 mb-2" id="totalVehicles">0</div><div class="text-slate-300">إجمالي المركبات</div></div>
        <div class="stats-card rounded-2xl p-6" onclick="showExpiringDocumentsReport()"><div class="text-3xl font-bold text-yellow-400 mb-2" id="expiringDocs">0</div><div class="text-slate-300">مستندات قيد الانتهاء</div></div>
      </div>
    </div>

    <!-- Main Cards Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-12">
      <div class="pro-card p-8 text-center animate-scale-in neon-border" style="animation-delay: 0.1s">
        <div class="w-20 h-20 bg-gradient-to-r from-rose-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"><i class="fas fa-users-cog text-white text-3xl"></i></div>
        <h3 class="text-2xl font-bold text-white mb-4 neon-text neon-text">إدارة الموظفين</h3><p class="text-slate-300 mb-8 leading-relaxed">إضافة وتعديل بيانات الموظفين وتسجيل أجور طلبات الشركات لهم</p><button class="pro-btn pro-btn-outlonclick="showEmployeesSection()" title="اختصار: Ctrl+E"><i class="fas fa-users"></i> إدارة الموظفين</button>
      </div>
       <div class="pro-card p-8 text-center animate-scale-in neon-border" style="animation-delay: 0.2s">
        <div class="w-20 h-20 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"><i class="fas fa-calendar-times text-white text-3xl"></i></div>
        <h3 class="text-2xl font-bold text-white mb-4">الإجازات والخصومات</h3><p class="text-slate-300 mb-8 leading-relaxed">إدارة إجازات الموظفين وتسجيل الخصومات المالية عليهم</p><button class="pro-btn pro-btn-outline w-full" onclick="showLeavesDeductionsSection()"><i class="fas fa-calendar-times"></i> فتح القسم</button>
      </div>
      <div class="pro-card p-8 text-center animate-scale-in neon-border" style="animation-delay: 0.3s">
        <div class="w-20 h-20 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"><i class="fas fa-folder-open text-white text-3xl"></i></div>
        <h3 class="text-2xl font-bold text-white mb-4 neon-text">مستندات الموظفين</h3><p class="text-slate-300 mb-8 leading-relaxed">إدارة وأرشفة المستندات الرسمية للموظفين وتواريخ انتهائها</p><button class="pro-btn pro-btn-outline w-full" onclick="showDocumentsEmployeeList()"><i class="fas fa-folder-open"></i> إدارة المستندات</button>
      </div>
      <div class="pro-card p-8 text-center animate-scale-in neon-border" style="animation-delay: 0.4s">
        <div class="w-20 h-20 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"><i class="fas fa-truck text-white text-3xl"></i></div>
        <h3 class="text-2xl font-bold text-white mb-4 neon-text">إدارة أسطول المركبات</h3><p class="text-slate-300 mb-8 leading-relaxed">متابعة حالة المركبات، الصيانة الدورية، وتعيين المركبات للسائقين</p><button class="pro-btn pro-btn-outline w-full" onclick="showFleetSection()" title="اختصار: Ctrl+F"><i class="fas fa-truck"></i> إدارة الأسطول</button>
      </div>
      <div class="pro-card p-8 text-center animate-scale-in neon-border" style="animation-delay: 0.5s">
        <div class="w-20 h-20 bg-gradient-to-r from-fuchsia-500 to-rose-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"><i class="fas fa-id-card-alt text-white text-3xl"></i></div>
        <h3 class="text-2xl font-bold text-white mb-4 neon-text">تقرير الموظف</h3><p class="text-slate-300 mb-8 leading-relaxed">عرض ملف شامل لبيانات الموظف ومستنداته وتنبيهات الانتهاء</p><button class="pro-btn pro-btn-outline w-full" onclick="showEmployeeReportSearch()"><i class="fas fa-address-book"></i> عرض التقارير</button>
      </div>
      <div class="pro-card p-8 text-center animate-scale-in neon-border" style="animation-delay: 0.8s">
        <div class="w-20 h-20 bg-gradient-to-r from-red-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg"><i class="fas fa-bell text-white text-3xl"></i></div>
        <h3 class="text-2xl font-bold text-white mb-4 neon-text">الإشعارات والتنبيهات</h3><p class="text-slate-300 mb-8 leading-relaxed">إرسال إشعارات للموظفين وتنبيهات الصيانة والمواعيد المهمة</p><button class="pro-btn pro-btn-outline w-full" onclick="showNotificationsSection()"><i class="fas fa-bell"></i> إدارة الإشعارات</button>
      </div>
    </div>

    <!-- Contact Information -->
    <div class="glass-card rounded-3xl p-8 mb-12 text-center animate-scale-in" style="animation-delay: 0.8s">
      <h3 class="text-2xl font-bold text-white mb-6">معلومات الاتصال</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h4 class="text-lg font-semibold text-rose-300 mb-4">شركة فور جي لتوصيل الطلبات</h4>
          <div class="space-y-3 text-slate-300">
            <div class="flex items-center justify-center gap-3"><i class="fas fa-phone text-green-400"></i><span>+965 57555554</span></div>
            <div class="flex items-center justify-center gap-3"><i class="fas fa-envelope text-rose-400"></i><span><EMAIL> (Placeholder)</span></div>
            <div class="flex items-center justify-center gap-3"><i class="fas fa-map-marker-alt text-red-400"></i><span>مدينة الكويت - شرق - قطعة 5 (Placeholder)</span></div>
          </div>
        </div>
        <div>
          <h4 class="text-lg font-semibold text-pink-300 mb-4">تطوير وتنفيذ</h4>
          <div class="space-y-3 text-slate-300"><div class="flex items-center justify-center gap-3"><i class="fas fa-laptop-code text-pink-400"></i><span class="font-semibold">MOHAMED T</span></div></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="glass-card rounded-none border-x-0 border-b-0 mt-16">
    <div class="container mx-auto px-6 py-8">
      <div class="text-center">
        <p class="text-pink-300 text-lg mb-2">© 2025 شركة فور جي لتوصيل الطلبات - جميع الحقوق محفوظة</p>
        <p class="text-pink-200"><i class="fas fa-code mr-2"></i>تطوير: <span class="font-bold text-white">MOHAMED T</span></p>
      </div>
    </div>
  </footer>

  <!-- Main Modal -->
  <div id="mainModal" class="modal"><div id="mainModalContent" class="modal-content rounded-3xl max-w-6xl animate-scale-in"></div></div>

  <!-- Confirmation Modal -->
  <div id="confirmModal" class="modal">
    <div class="modal-content rounded-3xl max-w-sm animate-scale-in p-8 text-center">
      <h3 id="confirmTitle" class="text-xl font-bold text-white mb-4"></h3>
      <p id="confirmText" class="text-slate-300 mb-6"></p>
      <div class="flex justify-center gap-4">
        <button id="confirmBtn" class="pro-btn bg-red-600 hover:bg-red-700 px-6 py-2 rounded-lg text-white font-semibold">تأكيد</button>
        <button id="cancelBtn" class="pro-btn-outline px-6 py-2 rounded-lg font-semibold">إلغاء</button>
      </div>
    </div>
  </div>
  
  <!-- Notification -->
  <div id="notification" class="notification"><div class="glass-card rounded-xl p-4 max-w-md mx-auto"><div class="flex items-center"><i id="notificationIcon" class="fas fa-info-circle text-blue-400 text-xl mr-3 ml-3"></i><span id="notificationText" class="text-white font-semibold"></span></div></div></div>

  <script>
    // --- DATABASE (LocalStorage) ---
    const DB = {
      getEmployees: () => JSON.parse(localStorage.getItem('employeesData') || '[]'),
      saveEmployees: (employees) => localStorage.setItem('employeesData', JSON.stringify(employees)),
      getDocuments: () => JSON.parse(localStorage.getItem('documentsData') || '{}'),
      saveDocuments: (documents) => localStorage.setItem('documentsData', JSON.stringify(documents)),
      getEmployeeOrders: () => JSON.parse(localStorage.getItem('employeeOrdersData') || '{}'),
      saveEmployeeOrders: (orders) => localStorage.setItem('employeeOrdersData', JSON.stringify(orders)),
      getCompanies: () => JSON.parse(localStorage.getItem('companiesData') || '[]'),
      saveCompanies: (companies) => localStorage.setItem('companiesData', JSON.stringify(companies)),
      getVehicles: () => JSON.parse(localStorage.getItem('vehiclesData') || '[]'),
      saveVehicles: (vehicles) => localStorage.setItem('vehiclesData', JSON.stringify(vehicles)),
      getLeaves: () => JSON.parse(localStorage.getItem('leavesData') || '{}'),
      saveLeaves: (leaves) => localStorage.setItem('leavesData', JSON.stringify(leaves)),
      getDeductions: () => JSON.parse(localStorage.getItem('deductionsData') || '{}'),
      saveDeductions: (deductions) => localStorage.setItem('deductionsData', JSON.stringify(deductions)),
    };

    // --- INITIALIZATION ---
    document.addEventListener('DOMContentLoaded', () => {
      console.log('🚀 Initializing Employee Management System...');
      initTheme();
      initSampleData();
      updateDateTime();
      updateDashboardStats();
      setInterval(updateDateTime, 1000);
      setInterval(updateDashboardStats, 30000);
      initKeyboardShortcuts();
      showNotification('أهلاً بك في نظام فور جي لإدارة الموظفين المحدث', 'success');
    });

    // --- KEYBOARD SHORTCUTS ---
    function initKeyboardShortcuts() {
      document.addEventListener('keydown', (e) => {
        if (e.ctrlKey || e.metaKey) {
          switch(e.key) {
            case 'e': e.preventDefault(); showEmployeesSection(); break;
            case 'f': e.preventDefault(); showFleetSection(); break;
            case 'p': e.preventDefault(); showEmployeeReportSearch(); break;
            case 's': e.preventDefault(); createBackup(); break;
          }
        }
        if (e.key === 'Escape') {
          hideModal();
        }
      });
    }

    function initSampleData() {
      if (!localStorage.getItem('employeesData')) {
        const sampleData = [
          { id: 101, name: 'أحمد محمود', job: 'driver', phone: '55123456', status: 'active', vehicle: '7-12345', salary: 350 },
          { id: 102, name: 'علي حسين', job: 'driver', phone: '55234567', status: 'active', vehicle: '7-23456', salary: 350 },
          { id: 201, name: 'فاطمة عبدالله', job: 'supervisor', phone: '55456789', status: 'active', vehicle: 'N/A', salary: 550 },
        ];
        DB.saveEmployees(sampleData);
      }
       if (!localStorage.getItem('documentsData')) {
        const tomorrow = new Date(); tomorrow.setDate(tomorrow.getDate() + 1);
        const nextMonth = new Date(); nextMonth.setMonth(nextMonth.getMonth() + 1);
        const docData = {
          101: [
              {docId: 1, name: 'البطاقة المدنية', type: 'civil_id', expiryDate: nextMonth.toISOString().split('T')[0]},
              {docId: 2, name: 'رخصة القيادة', type: 'license', expiryDate: '2026-05-20'},
              {docId: 3, name: 'جواز السفر', type: 'passport', expiryDate: '2025-01-15'},
          ],
          102: [
              {docId: 4, name: 'بطاقة عافية', type: 'health_card', expiryDate: tomorrow.toISOString().split('T')[0]}
          ]
        };
        DB.saveDocuments(docData);
      }
      if (!localStorage.getItem('companiesData')) {
          const companies = ['Deliveroo', '4G', 'Amazon', 'Speedrago', 'Talabat'];
          DB.saveCompanies(companies);
      }
      if (!localStorage.getItem('leavesData')) {
        const leaves = { 
            101: [{id: 1, date: new Date().toISOString(), type: 'leave', reason: 'إجازة سنوية', startDate: '2025-08-01', endDate: '2025-08-10'}],
            102: [{id: 2, date: new Date().toISOString(), type: 'lateness', reason: 'تأخر عن الحضور', duration: '30 دقيقة', action: 'إنذار شفوي'}]
        };
        DB.saveLeaves(leaves);
      }
      if (!localStorage.getItem('deductionsData')) {
        const deductions = { 102: [{id: 1, date: new Date().toISOString(), reason: 'غياب', amount: 20}] };
        DB.saveDeductions(deductions);
      }
      if (!localStorage.getItem('vehiclesData')) {
        const vehicles = [
          { id: 1, plateNumber: '7-12345', model: 'تويوتا كامري', year: 2020, status: 'in_use', assignedDriver: 101, lastMaintenance: '2024-01-15' },
          { id: 2, plateNumber: '7-23456', model: 'نيسان التيما', year: 2019, status: 'in_use', assignedDriver: 102, lastMaintenance: '2024-02-10' },
          { id: 3, plateNumber: '7-34567', model: 'هونداي إلنترا', year: 2021, status: 'available', assignedDriver: null, lastMaintenance: '2024-01-20' },
          { id: 4, plateNumber: '7-45678', model: 'كيا سيراتو', year: 2018, status: 'maintenance', assignedDriver: null, lastMaintenance: '2024-03-01' }
        ];
        DB.saveVehicles(vehicles);
      }
    }

    // --- UI & DATE/TIME ---
    function updateDateTime() {
      const now = new Date();
      const dateOptions = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
      const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false };
      document.getElementById('currentDate').textContent = now.toLocaleDateString('ar-KW', dateOptions);
      document.getElementById('currentTime').textContent = now.toLocaleTimeString('en-GB', timeOptions);
    }
    
    // --- DASHBOARD ---
    function updateDashboardStats() {
      const employees = DB.getEmployees();
      const vehicles = DB.getVehicles();
      const allDocs = DB.getDocuments();

      let expiringDocsCount = 0;
      const now = new Date();
      for (const empId in allDocs) {
          allDocs[empId].forEach(doc => {
              if (doc.expiryDate) {
                  const expiry = new Date(doc.expiryDate);
                  const daysDiff = (expiry - now) / (1000 * 60 * 60 * 24);
                  if (daysDiff <= 30 && daysDiff > 0) {
                      expiringDocsCount++;
                  }
              }
          });
      }

      const stats = {
        totalEmployees: employees.length,
        totalVehicles: vehicles.length,
        expiringDocs: expiringDocsCount
      };
      animateNumber('totalEmployees', stats.totalEmployees);
      animateNumber('totalVehicles', stats.totalVehicles);
      animateNumber('expiringDocs', stats.expiringDocs);
    }

    function animateNumber(elementId, end) {
      const element = document.getElementById(elementId);
      if (!element) return;
      const start = parseInt(element.textContent.replace(/,/g, '')) || 0;
      if (start === end) return;
      const duration = 1500;
      const startTime = performance.now();
      
      function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easeOut = 1 - Math.pow(1 - progress, 3);
        const current = Math.floor(start + (end - start) * easeOut);
        element.textContent = current.toLocaleString();
        if (progress < 1) requestAnimationFrame(update);
      }
      requestAnimationFrame(update);
    }

    // --- MODALS & NOTIFICATION ---
    const mainModal = document.getElementById('mainModal');
    const mainModalContent = document.getElementById('mainModalContent');
    const confirmModal = document.getElementById('confirmModal');

    function showModal(content, targetModal = mainModal, targetContent = mainModalContent) {
      targetContent.innerHTML = content;
      targetModal.classList.add('show');
    }

    function hideModal(targetModal = mainModal) {
      targetModal.classList.remove('show');
      if (targetModal.querySelector('.modal-content')) {
        targetModal.querySelector('.modal-content').innerHTML = '';
      }
    }
    
    mainModal.addEventListener('click', (e) => { if (e.target === mainModal) hideModal(mainModal); });

    function showConfirm(title, text, onConfirm) {
        document.getElementById('confirmTitle').textContent = title;
        document.getElementById('confirmText').textContent = text;
        confirmModal.classList.add('show');

        const confirmBtn = document.getElementById('confirmBtn');
        const cancelBtn = document.getElementById('cancelBtn');

        const close = () => {
            confirmModal.classList.remove('show');
            confirmBtn.replaceWith(confirmBtn.cloneNode(true));
            cancelBtn.replaceWith(cancelBtn.cloneNode(true));
        };
        
        document.getElementById('confirmBtn').onclick = () => { onConfirm(); close(); };
        document.getElementById('cancelBtn').onclick = close;
    }

    function showNotification(message, type = 'info') {
      const notification = document.getElementById('notification');
      const icon = document.getElementById('notificationIcon');
      const text = document.getElementById('notificationText');
      const config = {
        success: { icon: 'fa-check-circle', color: 'text-green-400' },
        error: { icon: 'fa-times-circle', color: 'text-red-400' },
        info: { icon: 'fa-info-circle', color: 'text-rose-400' }
      };
      const typeConfig = config[type] || config.info;
      icon.className = `fas ${typeConfig.icon} ${typeConfig.color} text-xl mr-3 ml-3`;
      text.textContent = message;

      if (navigator.vibrate && type === 'success') {
        navigator.vibrate([100, 50, 100]);
      }

      notification.classList.add('show');
      setTimeout(() => notification.classList.remove('show'), 4000);
    }

    // --- EMPLOYEE MANAGEMENT ---
    function showEmployeesSection(searchTerm = '', filterJob = '', filterStatus = '') {
        let employees = DB.getEmployees();

        if (searchTerm) {
            employees = employees.filter(emp =>
                emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                emp.phone.includes(searchTerm) ||
                emp.id.toString().includes(searchTerm)
            );
        }
        if (filterJob) {
            employees = employees.filter(emp => emp.job === filterJob);
        }
        if (filterStatus) {
            employees = employees.filter(emp => emp.status === filterStatus);
        }

        const jobTitles = { 'driver': 'سائق', 'supervisor': 'مشرف', 'admin': 'إداري' };
        const statusText = { 'active': 'نشط', 'on_leave': 'إجازة', 'inactive': 'غير نشط' };

        let employeeCards = employees.map(emp => `
            <div class="pro-card p-4 rounded-2xl flex flex-col justify-between">
                <div>
                    <div class="flex items-center justify-between mb-3">
                        <span class="category-badge job-${emp.job}">${jobTitles[emp.job]}</span>
                        <span class="status-badge status-${emp.status}">${statusText[emp.status]}</span>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">${emp.name}</h3>
                    <div class="text-pink-300 text-sm space-y-1">
                        <div class="flex items-center"><i class="fas fa-id-card text-pink-400 w-5 text-center mr-2"></i>${emp.id}</div>
                        <div class="flex items-center"><i class="fas fa-phone text-green-400 w-5 text-center mr-2"></i>${emp.phone}</div>
                        ${emp.job === 'driver' ? `<div class="flex items-center"><i class="fas fa-truck text-orange-400 w-5 text-center mr-2"></i>${emp.vehicle || 'N/A'}</div>` : ''}
                    </div>
                </div>
                <div class="flex gap-2 mt-4">
                    <button onclick="showEmployeeForm(${emp.id})" class="flex-1 pro-btn-outline text-xs py-2 rounded-md"><i class="fas fa-edit"></i> تعديل / إضافة طلب</button>
                    <button onclick="deleteEmployee(${emp.id})" class="flex-1 pro-btn-outline text-xs py-2 rounded-md border-red-500/50 text-red-400 hover:bg-red-500/20 hover:text-red-300"><i class="fas fa-trash"></i> حذف</button>
                </div>
            </div>`).join('');

        const content = `
            <div class="p-6 border-b border-white/20 sticky top-0 bg-slate-900/50 backdrop-blur-sm z-10">
                <div class="flex items-center justify-between"><h2 class="text-2xl font-bold text-white">إدارة الموظفين</h2><button onclick="hideModal()" class="text-white hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button></div>
                <div class="mt-4 space-y-4">
                    <div class="flex gap-4">
                        <input type="text" id="searchInput" onkeyup="filterEmployees()" value="${searchTerm}" placeholder="ابحث بالاسم، الهاتف، أو الرقم..." class="flex-1 form-input rounded-lg px-4 py-2">
                        <button onclick="showEmployeeForm()" class="pro-btn px-6 py-2 rounded-lg text-white font-semibold whitespace-nowrap"><i class="fas fa-plus"></i> إضافة موظف</button>
                    </div>
                    <div class="flex gap-4 flex-wrap">
                        <select id="jobFilter" onchange="filterEmployees()" class="form-input rounded-lg px-4 py-2">
                            <option value="">جميع الوظائف</option>
                            <option value="driver" ${filterJob === 'driver' ? 'selected' : ''}>سائق</option>
                            <option value="supervisor" ${filterJob === 'supervisor' ? 'selected' : ''}>مشرف</option>
                            <option value="admin" ${filterJob === 'admin' ? 'selected' : ''}>إداري</option>
                        </select>
                        <select id="statusFilter" onchange="filterEmployees()" class="form-input rounded-lg px-4 py-2">
                            <option value="">جميع الحالات</option>
                            <option value="active" ${filterStatus === 'active' ? 'selected' : ''}>نشط</option>
                            <option value="on_leave" ${filterStatus === 'on_leave' ? 'selected' : ''}>إجازة</option>
                            <option value="inactive" ${filterStatus === 'inactive' ? 'selected' : ''}>غير نشط</option>
                        </select>
                        <button onclick="exportEmployeeData()" class="pro-btn-outline px-4 py-2 rounded-lg"><i class="fas fa-download"></i> تصدير</button>
                        <button onclick="document.getElementById('importFileInput').click()" class="pro-btn-outline px-4 py-2 rounded-lg"><i class="fas fa-upload"></i> استيراد</button>
                        <input type="file" id="importFileInput" class="hidden" accept=".csv" onchange="importEmployeeData(event)">
                        <button onclick="showBackupOptions()" class="pro-btn-outline px-4 py-2 rounded-lg"><i class="fas fa-database"></i> نسخ احتياطي</button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <div class="mb-4 text-slate-300">
                    <span>عدد النتائج: <span class="text-white font-bold">${employees.length}</span></span>
                </div>
                <div id="employeeList" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">${employeeCards || `<p class="text-slate-400 col-span-full text-center">لا يوجد موظفين لعرضهم.</p>`}</div>
            </div>`;
        showModal(content);
    }
    
    function showEmployeeForm(id = null) {
        const isEditing = id !== null;
        const employee = isEditing ? DB.getEmployees().find(e => e.id === id) : {};
        const title = isEditing ? 'تعديل بيانات موظف' : 'إضافة موظف جديد';
        const companies = DB.getCompanies();
        const companyOptions = companies.map(c => `<option value="${c}">${c}</option>`).join('');

        const allOrders = DB.getEmployeeOrders();
        const employeeOrders = (isEditing ? allOrders[id] : []) || [];
        const recentOrdersList = employeeOrders.slice(-5).reverse().map(order => `
            <li class="flex justify-between p-2 bg-slate-800/50 rounded-md">
                <span>${order.companyName}</span>
                <span class="font-bold text-green-400">${parseFloat(order.wage).toFixed(2)} د.ك</span>
            </li>`).join('');

        const formHtml = `
            <div class="p-6 border-b border-white/20"><h2 class="text-2xl font-bold text-white text-center">${title}</h2></div>
            <div class="p-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
                <form id="employeeForm">
                    <h3 class="text-xl font-bold text-white mb-4">بيانات الموظف</h3>
                    <input type="hidden" name="id" value="${employee.id || ''}">
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div><label class="text-slate-300 mb-2 block">الاسم الكامل</label><input type="text" name="name" value="${employee.name || ''}" class="w-full form-input rounded-lg px-4 py-2" required></div>
                            <div><label class="text-slate-300 mb-2 block">رقم الهاتف</label><input type="tel" name="phone" value="${employee.phone || ''}" class="w-full form-input rounded-lg px-4 py-2" required></div>
                            <div><label class="text-slate-300 mb-2 block">الوظيفة</label><select name="job" class="w-full form-input rounded-lg px-4 py-2" required><option value="driver" ${employee.job === 'driver' ? 'selected' : ''}>سائق</option><option value="supervisor" ${employee.job === 'supervisor' ? 'selected' : ''}>مشرف</option><option value="admin" ${employee.job === 'admin' ? 'selected' : ''}>إداري</option></select></div>
                            <div><label class="text-slate-300 mb-2 block">المركبة (للسائقين)</label><input type="text" name="vehicle" value="${employee.vehicle || ''}" class="w-full form-input rounded-lg px-4 py-2"></div>
                            <div><label class="text-slate-300 mb-2 block">الراتب الأساسي</label><input type="number" name="salary" value="${employee.salary || 0}" class="w-full form-input rounded-lg px-4 py-2" required></div>
                            <div><label class="text-slate-300 mb-2 block">الحالة</label><select name="status" class="w-full form-input rounded-lg px-4 py-2" required><option value="active" ${employee.status === 'active' ? 'selected' : ''}>نشط</option><option value="on_leave" ${employee.status === 'on_leave' ? 'selected' : ''}>إجازة</option><option value="inactive" ${employee.status === 'inactive' ? 'selected' : ''}>غير نشط</option></select></div>
                        </div>
                        <div class="flex justify-end gap-4 pt-4">
                            <button type="button" onclick="showEmployeesSection()" class="pro-btn-outline px-6 py-2 rounded-lg font-semibold">إلغاء</button>
                            <button type="submit" class="pro-btn px-6 py-2 rounded-lg text-white font-semibold">حفظ البيانات</button>
                        </div>
                    </div>
                </form>
                ${isEditing ? `
                <div class="border-t lg:border-t-0 lg:border-r border-slate-700 pt-8 lg:pt-0 lg:pr-8">
                    <h3 class="text-xl font-bold text-white mb-4">تسجيل طلب شركة</h3>
                    <form id="employeeOrderForm" class="space-y-3">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
                            <select name="companyName" class="form-input rounded-lg p-2">${companyOptions}</select>
                            <input type="number" step="0.01" name="wage" placeholder="أجر الطلب" class="form-input rounded-lg p-2 text-center">
                        </div>
                        <div class="text-left pt-2">
                            <button type="submit" class="pro-btn px-6 py-2 rounded-lg text-white font-semibold w-full md:w-auto">حفظ الطلب</button>
                        </div>
                    </form>
                    <h4 class="text-lg font-semibold text-white mt-6 mb-3">آخر الطلبات المسجلة</h4>
                    <ul class="space-y-2">${recentOrdersList || '<li class="text-slate-400">لا توجد طلبات مسجلة.</li>'}</ul>
                </div>` : ''}
            </div>`;
        showModal(formHtml);
        document.getElementById('employeeForm').addEventListener('submit', (e) => saveEmployee(e, isEditing));
        if(isEditing) {
            document.getElementById('employeeOrderForm').addEventListener('submit', (e) => saveEmployeeOrder(e, id));
        }
    }

    function saveEmployee(event, isEditing) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const employeeData = Object.fromEntries(formData.entries());
        let employees = DB.getEmployees();
        
        employeeData.salary = parseFloat(employeeData.salary);
        
        if (isEditing) {
            const index = employees.findIndex(e => e.id == employeeData.id);
            employees[index] = { ...employees[index], ...employeeData };
            showNotification('تم تحديث بيانات الموظف بنجاح', 'success');
        } else {
            employeeData.id = new Date().getTime();
            employees.push(employeeData);
            showNotification('تمت إضافة الموظف بنجاح', 'success');
        }
        
        DB.saveEmployees(employees);
        updateDashboardStats();
        if (isEditing) {
            showEmployeeForm(employeeData.id);
        } else {
            showEmployeesSection();
        }
    }

    function saveEmployeeOrder(event, empId) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const companyName = formData.get('companyName');
        const wage = parseFloat(formData.get('wage'));

        if(companyName && wage > 0) {
            let allOrders = DB.getEmployeeOrders();
            if(!allOrders[empId]) {
                allOrders[empId] = [];
            }
            allOrders[empId].push({ date: new Date().toISOString(), companyName, wage });
            DB.saveEmployeeOrders(allOrders);
            showNotification('تم حفظ الطلب للموظف', 'success');
            showEmployeeForm(empId);
        } else {
            showNotification('الرجاء إدخال بيانات الطلب بشكل صحيح', 'error');
        }
    }

    function deleteEmployee(id) {
        showConfirm('تأكيد الحذف', 'هل أنت متأكد من رغبتك في حذف هذا الموظف؟ سيتم حذف جميع بياناته ومستنداته.', () => {
            let employees = DB.getEmployees().filter(e => e.id !== id);
            let documents = DB.getDocuments();
            let orders = DB.getEmployeeOrders();
            let leaves = DB.getLeaves();
            let deductions = DB.getDeductions();
            delete documents[id];
            delete orders[id];
            delete leaves[id];
            delete deductions[id];
            DB.saveEmployees(employees);
            DB.saveDocuments(documents);
            DB.saveEmployeeOrders(orders);
            DB.saveLeaves(leaves);
            DB.saveDeductions(deductions);
            showNotification('تم حذف الموظف بنجاح', 'success');
            updateDashboardStats();
            showEmployeesSection();
        });
    }

    function filterEmployees() {
        const searchTerm = document.getElementById('searchInput').value;
        const jobFilter = document.getElementById('jobFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        showEmployeesSection(searchTerm, jobFilter, statusFilter);
    }
    
    function importEmployeeData(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            const text = e.target.result;
            
            showConfirm('تأكيد الاستيراد', 'سيتم إضافة الموظفين الجدد وتحديث بيانات الموظفين الحاليين بناءً على الرقم الوظيفي. هل تريد المتابعة؟', () => {
                try {
                    const rows = text.split('\n').slice(1);
                    let employees = DB.getEmployees();
                    let importedCount = 0;
                    let updatedCount = 0;
                    let errorCount = 0;

                    rows.forEach((row, index) => {
                        if (row.trim() === '') return;
                        
                        const columns = row.split(',');
                        
                        if (columns.length < 7) {
                            console.error(`Skipping invalid row #${index + 2}:`, row);
                            errorCount++;
                            return;
                        }

                        const employeeData = {
                            id: parseInt(columns[0]),
                            name: columns[1].replace(/"/g, ''),
                            job: columns[2].replace(/"/g, ''),
                            phone: columns[3].replace(/"/g, ''),
                            status: columns[4].replace(/"/g, ''),
                            vehicle: columns[5].replace(/"/g, ''),
                            salary: parseFloat(columns[6])
                        };

                        if (isNaN(employeeData.id) || !employeeData.name) {
                            console.error(`Skipping row with missing or invalid ID/Name #${index + 2}:`, row);
                            errorCount++;
                            return;
                        }

                        const existingIndex = employees.findIndex(emp => emp.id === employeeData.id);
                        if (existingIndex !== -1) {
                            employees[existingIndex] = { ...employees[existingIndex], ...employeeData };
                            updatedCount++;
                        } else {
                            employees.push(employeeData);
                            importedCount++;
                        }
                    });

                    DB.saveEmployees(employees);
                    let message = `اكتمل الاستيراد. تمت إضافة ${importedCount} موظف، وتحديث ${updatedCount} موظف.`;
                    if(errorCount > 0) {
                        message += ` تم تخطي ${errorCount} سجل لوجود أخطاء.`;
                    }
                    showNotification(message, 'success');
                    updateDashboardStats();
                    showEmployeesSection();

                } catch (err) {
                    showNotification('حدث خطأ أثناء معالجة الملف. تأكد من أن الملف بصيغة CSV صحيحة.', 'error');
                    console.error("Import error:", err);
                }
            });
        };
        
        reader.onerror = function() {
            showNotification('فشل في قراءة الملف.', 'error');
        };

        reader.readAsText(file);
        event.target.value = '';
    }

    function exportEmployeeData() {
        const employees = DB.getEmployees();
        const csvContent = "data:text/csv;charset=utf-8,"
            + "الرقم,الاسم,الوظيفة,الهاتف,الحالة,المركبة,الراتب\n"
            + employees.map(emp =>
                `${emp.id},"${emp.name}","${emp.job}","${emp.phone}","${emp.status}","${emp.vehicle || 'N/A'}",${emp.salary || 0}`
            ).join("\n");

        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", `employees_${new Date().toISOString().split('T')[0]}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showNotification('تم تصدير بيانات الموظفين بنجاح', 'success');
    }

    function showBackupOptions() {
        const content = `
            <div class="p-6 border-b border-white/20">
                <h2 class="text-2xl font-bold text-white text-center">النسخ الاحتياطي واستعادة البيانات</h2>
            </div>
            <div class="p-8">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="pro-card p-6 rounded-2xl">
                        <h3 class="text-xl font-bold text-white mb-4"><i class="fas fa-download text-green-400 mr-2"></i>إنشاء نسخة احتياطية</h3>
                        <p class="text-slate-300 mb-4">احفظ جميع بيانات النظام في ملف واحد</p>
                        <button onclick="createBackup()" class="pro-btn w-full py-2 rounded-lg">تحميل النسخة الاحتياطية</button>
                    </div>
                    <div class="pro-card p-6 rounded-2xl">
                        <h3 class="text-xl font-bold text-white mb-4"><i class="fas fa-upload text-rose-400 mr-2"></i>استعادة البيانات</h3>
                        <p class="text-slate-300 mb-4">استعد البيانات من ملف نسخة احتياطية</p>
                        <input type="file" id="backupFile" accept=".json" class="hidden">
                        <button onclick="document.getElementById('backupFile').click()" class="pro-btn-outline w-full py-2 rounded-lg">اختيار ملف الاستعادة</button>
                    </div>
                </div>
                <div class="mt-8 text-center">
                    <button onclick="hideModal()" class="pro-btn-outline px-6 py-2 rounded-lg">إغلاق</button>
                </div>
            </div>`;
        showModal(content);

        document.getElementById('backupFile').addEventListener('change', restoreBackup);
    }

    function createBackup() {
        const backupData = {
            employees: DB.getEmployees(),
            documents: DB.getDocuments(),
            employeeOrders: DB.getEmployeeOrders(),
            companies: DB.getCompanies(),
            leaves: DB.getLeaves(),
            deductions: DB.getDeductions(),
            vehicles: DB.getVehicles(),
            timestamp: new Date().toISOString(),
            version: '1.2' // Updated version
        };

        const dataStr = JSON.stringify(backupData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `4g_backup_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
    }

    function restoreBackup(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const backupData = JSON.parse(e.target.result);

                showConfirm('تأكيد الاستعادة', 'هل أنت متأكد من استعادة البيانات؟ سيتم استبدال جميع البيانات الحالية.', () => {
                    DB.saveEmployees(backupData.employees || []);
                    DB.saveDocuments(backupData.documents || {});
                    DB.saveEmployeeOrders(backupData.employeeOrders || {});
                    DB.saveCompanies(backupData.companies || []);
                    DB.saveLeaves(backupData.leaves || {});
                    DB.saveDeductions(backupData.deductions || {});
                    DB.saveVehicles(backupData.vehicles || []);
                    
                    showNotification('تم استعادة البيانات بنجاح', 'success');
                    updateDashboardStats();
                    hideModal();

                    setTimeout(() => location.reload(), 2000);
                });
            } catch (error) {
                showNotification('خطأ في قراءة ملف النسخة الاحتياطية', 'error');
            }
        };
        reader.readAsText(file);
    }

    // --- THEME MANAGEMENT ---
    function toggleTheme() {
        const body = document.body;
        const themeIcon = document.getElementById('themeIcon');
        const themeText = document.getElementById('themeText');

        if (body.classList.contains('light-theme')) {
            body.classList.remove('light-theme');
            themeIcon.className = 'fas fa-moon';
            themeText.textContent = 'الوضع المظلم';
            localStorage.setItem('theme', 'dark');
        } else {
            body.classList.add('light-theme');
            themeIcon.className = 'fas fa-sun';
            themeText.textContent = 'الوضع الفاتح';
            localStorage.setItem('theme', 'light');
        }
    }

    function initTheme() {
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'light') {
            document.body.classList.add('light-theme');
            document.getElementById('themeIcon').className = 'fas fa-sun';
            document.getElementById('themeText').textContent = 'الوضع الفاتح';
        }
    }

    // --- LEAVES AND DEDUCTIONS ---
    function showLeavesDeductionsSection(searchTerm = '') {
        const employees = DB.getEmployees().filter(emp => emp.name.toLowerCase().includes(searchTerm.toLowerCase()));
        const employeeButtons = employees.map(emp => `<button onclick="showEmployeeLeaveDeductionModal(${emp.id})" class="pro-card p-4 text-center rounded-lg">${emp.name}</button>`).join('');
        
        const content = `
            <div class="p-6 border-b border-white/20 sticky top-0 bg-slate-900/50 backdrop-blur-sm z-10">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-bold text-white">الإجازات والخصومات</h2>
                    <button onclick="hideModal()" class="text-white hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button>
                </div>
                <div class="mt-4">
                    <input type="text" onkeyup="showLeavesDeductionsSection(this.value)" value="${searchTerm}" placeholder="ابحث عن موظف..." class="w-full form-input rounded-lg px-4 py-2">
                </div>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    ${employeeButtons || `<p class="text-slate-400 col-span-full text-center">لم يتم العثور على موظفين.</p>`}
                </div>
            </div>`;
        showModal(content);
    }

    function showEmployeeLeaveDeductionModal(empId) {
        const employee = DB.getEmployees().find(e => e.id === empId);
        const leaves = DB.getLeaves()[empId] || [];
        const deductions = DB.getDeductions()[empId] || [];

        const leavesList = leaves.map(l => {
            if (l.type === 'leave') {
                 return `<li class="p-3 bg-slate-800/50 rounded-md">
                            <div class="flex justify-between items-center">
                                <div class="flex items-center gap-3"><i class="fas fa-calendar-day text-orange-400"></i> ${l.reason} (إجازة)</div>
                                <div class="text-sm text-slate-400">${l.startDate} إلى ${l.endDate}</div>
                            </div>
                        </li>`;
            } else { // lateness
                return `<li class="p-3 bg-slate-800/50 rounded-md">
                            <div class="flex justify-between items-center mb-1">
                                <div class="flex items-center gap-3"><i class="fas fa-clock text-yellow-400"></i> ${l.reason} (تأخير)</div>
                                <div class="font-bold">${l.duration}</div>
                            </div>
                            <div class="text-sm text-slate-300 pr-7">الإجراء: ${l.action}</div>
                        </li>`;
            }
        }).join('');

        const deductionsList = deductions.map(d => `<li class="flex justify-between p-2 bg-slate-800/50 rounded-md"><div>${d.reason}</div><div class="text-red-400">${d.amount.toFixed(2)} د.ك</div></li>`).join('');

        const content = `
            <div class="p-6 border-b border-white/20 flex justify-between items-center">
                <h2 class="text-2xl font-bold text-white">إجازات وخصومات: ${employee.name}</h2>
                <button onclick="showLeavesDeductionsSection()" class="text-white hover:text-rose-400 text-xl"><i class="fas fa-arrow-right"></i></button>
            </div>
            <div class="p-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-bold text-white mb-4">تسجيل إجازة / تأخير</h3>
                    <form id="leaveOrLatenessForm" class="space-y-4">
                        <select name="type" id="entryTypeSelect" class="w-full form-input rounded-lg p-2">
                            <option value="leave">إجازة</option>
                            <option value="lateness">تأخير</option>
                        </select>
                        <div id="dynamicInputsContainer"></div>
                        <button type="submit" class="pro-btn w-full py-2 rounded-lg">حفظ</button>
                    </form>
                    <h4 class="text-lg font-semibold text-white mt-6 mb-3">سجل الإجازات والتأخير</h4>
                    <ul class="space-y-2">${leavesList || '<li>لا توجد إدخالات.</li>'}</ul>
                </div>
                <div class="border-t lg:border-t-0 lg:border-r border-slate-700 pt-8 lg:pt-0 lg:pr-8">
                    <h3 class="text-xl font-bold text-white mb-4">تسجيل خصم مالي</h3>
                    <form id="deductionForm" class="space-y-3">
                        <input type="text" name="reason" placeholder="سبب الخصم" class="w-full form-input rounded-lg p-2" required>
                        <input type="number" step="0.01" name="amount" placeholder="المبلغ" class="w-full form-input rounded-lg p-2" required>
                        <button type="submit" class="pro-btn w-full py-2 rounded-lg">حفظ الخصم</button>
                    </form>
                    <h4 class="text-lg font-semibold text-white mt-6 mb-3">سجل الخصومات</h4>
                    <ul class="space-y-2">${deductionsList || '<li>لا توجد خصومات مسجلة.</li>'}</ul>
                </div>
            </div>`;
        showModal(content);
        
        const entryTypeSelect = document.getElementById('entryTypeSelect');
        const dynamicInputsContainer = document.getElementById('dynamicInputsContainer');
        
        const updateLeaveForm = () => {
            const type = entryTypeSelect.value;
            if (type === 'leave') {
                dynamicInputsContainer.innerHTML = `
                    <div class="grid grid-cols-2 gap-4">
                        <div><label class="text-slate-300 mb-1 block">بداية الإجازة</label><input type="date" name="startDate" class="w-full form-input rounded-lg p-2" required></div>
                        <div><label class="text-slate-300 mb-1 block">نهاية الإجازة</label><input type="date" name="endDate" class="w-full form-input rounded-lg p-2" required></div>
                    </div>
                    <input type="text" name="reason" placeholder="سبب الإجازة" class="w-full form-input rounded-lg p-2" required>`;
            } else { // lateness
                dynamicInputsContainer.innerHTML = `
                    <input type="text" name="duration" placeholder="مدة التأخير (مثال: 30 دقيقة)" class="w-full form-input rounded-lg p-2" required>
                    <input type="text" name="reason" placeholder="سبب التأخير" class="w-full form-input rounded-lg p-2" required>
                    <select name="action" class="w-full form-input rounded-lg p-2">
                        <option value="لا يوجد">لا يوجد إجراء</option>
                        <option value="إنذار شفوي">إنذار شفوي</option>
                        <option value="إنذار كتابي">إنذار كتابي</option>
                        <option value="خصم من الراتب">خصم من الراتب</option>
                    </select>`;
            }
        };
        
        entryTypeSelect.addEventListener('change', updateLeaveForm);
        updateLeaveForm();
        
        document.getElementById('leaveOrLatenessForm').addEventListener('submit', e => saveLeaveOrLateness(e, empId));
        document.getElementById('deductionForm').addEventListener('submit', e => saveDeduction(e, empId));
    }

    function saveLeaveOrLateness(event, empId) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const type = formData.get('type');
        const allLeaves = DB.getLeaves();
        if (!allLeaves[empId]) allLeaves[empId] = [];

        const newEntry = { id: new Date().getTime(), date: new Date().toISOString(), type };

        if (type === 'leave') {
            newEntry.startDate = formData.get('startDate');
            newEntry.endDate = formData.get('endDate');
            newEntry.reason = formData.get('reason');
            if (!newEntry.startDate || !newEntry.endDate || !newEntry.reason) {
                showNotification('الرجاء تعبئة جميع حقول الإجازة', 'error');
                return;
            }
        } else {
            newEntry.duration = formData.get('duration');
            newEntry.reason = formData.get('reason');
            newEntry.action = formData.get('action');
             if (!newEntry.duration || !newEntry.reason) {
                showNotification('الرجاء تعبئة مدة وسبب التأخير', 'error');
                return;
            }
        }
        
        allLeaves[empId].push(newEntry);
        DB.saveLeaves(allLeaves);
        showNotification('تم تسجيل الإدخال بنجاح', 'success');
        showEmployeeLeaveDeductionModal(empId);
    }

    function saveDeduction(event, empId) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const reason = formData.get('reason');
        const amount = parseFloat(formData.get('amount'));
        if (reason && amount > 0) {
            const allDeductions = DB.getDeductions();
            if (!allDeductions[empId]) allDeductions[empId] = [];
            allDeductions[empId].push({ id: new Date().getTime(), date: new Date().toISOString(), reason, amount });
            DB.saveDeductions(allDeductions);
            showNotification('تم تسجيل الخصم بنجاح', 'success');
            showEmployeeLeaveDeductionModal(empId);
            updateDashboardStats();
        }
    }
    
    // --- DOCUMENT MANAGEMENT (UPDATED) ---
    function showDocumentsEmployeeList(searchTerm = '') {
        const employees = DB.getEmployees().filter(emp => emp.name.toLowerCase().includes(searchTerm.toLowerCase()));
        const employeeButtons = employees.map(emp => `<button onclick="showEmployeeDocs(${emp.id})" class="pro-card p-4 text-center rounded-lg">${emp.name}</button>`).join('');
        
        const content = `
            <div class="p-6 border-b border-white/20 sticky top-0 bg-slate-900/50 backdrop-blur-sm z-10">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-bold text-white">مستندات الموظفين</h2>
                    <button onclick="hideModal()" class="text-white hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button>
                </div>
                <div class="mt-4">
                    <input type="text" onkeyup="showDocumentsEmployeeList(this.value)" value="${searchTerm}" placeholder="ابحث عن موظف..." class="w-full form-input rounded-lg px-4 py-2">
                </div>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    ${employeeButtons || `<p class="text-slate-400 col-span-full text-center">لم يتم العثور على موظفين.</p>`}
                </div>
            </div>`;
        showModal(content);
    }

    function showEmployeeDocs(empId) {
        const employee = DB.getEmployees().find(e => e.id === empId);
        const allDocs = DB.getDocuments();
        const employeeDocs = allDocs[empId] || [];
        const docTypes = { 
            civil_id: 'البطاقة المدنية', 
            passport: 'جواز السفر', 
            license: 'رخصة قيادة', 
            health_card: 'كرت الصحة', 
            vehicle_registration: 'دفتر السيارة', 
            contract: 'عقد عمل', 
            other: 'أخرى' 
        };

        const docList = employeeDocs.map(doc => `
            <div class="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
                <div class="flex items-center gap-3">
                    <i class="fas fa-file-alt text-rose-400"></i>
                    <div>
                        <span class="text-white">${doc.name} (${docTypes[doc.type] || doc.type})</span>
                        ${doc.expiryDate ? `<div class="text-xs text-slate-400">ينتهي في: ${doc.expiryDate}</div>` : ''}
                    </div>
                </div>
                <button onclick="deleteDocument(${empId}, ${doc.docId})" class="text-red-400 hover:text-red-300"><i class="fas fa-trash"></i></button>
            </div>`).join('');

        const content = `
            <div class="p-6 border-b border-white/20"><div class="flex items-center justify-between"><h2 class="text-2xl font-bold text-white">مستندات: ${employee.name}</h2><button onclick="showDocumentsEmployeeList()" class="text-white hover:text-rose-400 text-xl"><i class="fas fa-arrow-right"></i></button></div></div>
            <div class="p-6">
                <form id="uploadDocForm" class="pro-card p-4 rounded-lg mb-6 space-y-3">
                    <h3 class="text-lg font-semibold text-white">إضافة مستند جديد</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <input type="text" id="docNameInput" placeholder="اسم/رقم المستند..." class="form-input rounded-lg px-4 py-2" required>
                        <select id="docTypeInput" class="form-input rounded-lg px-4 py-2">
                            <option value="civil_id">البطاقة المدنية</option>
                            <option value="passport">جواز سفر</option>
                            <option value="license">رخصة قيادة</option>
                            <option value="health_card">كرت الصحة</option>
                            <option value="vehicle_registration">دفتر السيارة</option>
                            <option value="contract">عقد عمل</option>
                            <option value="other">أخرى</option>
                        </select>
                        <input type="date" id="docExpiryInput" class="form-input rounded-lg px-4 py-2">
                    </div>
                    <button type="submit" class="pro-btn w-full py-2 rounded-lg text-white font-semibold"><i class="fas fa-upload"></i> رفع</button>
                </form>
                <div class="space-y-3">${docList || '<p class="text-slate-400 text-center">لا توجد مستندات لهذا الموظف.</p>'}</div>
            </div>`;
        showModal(content);
        document.getElementById('uploadDocForm').addEventListener('submit', (e) => {
            e.preventDefault();
            const docName = document.getElementById('docNameInput').value;
            const docType = document.getElementById('docTypeInput').value;
            const expiryDate = document.getElementById('docExpiryInput').value;
            if(docName) uploadDocument(empId, docName, docType, expiryDate);
        });
    }

    function uploadDocument(empId, docName, docType, expiryDate) {
        let allDocs = DB.getDocuments();
        if (!allDocs[empId]) allDocs[empId] = [];
        const newDoc = { 
            docId: new Date().getTime(), 
            name: docName, 
            type: docType,
            expiryDate: expiryDate || null
        };
        allDocs[empId].push(newDoc);
        DB.saveDocuments(allDocs);
        showNotification('تم رفع المستند بنجاح', 'success');
        showEmployeeDocs(empId);
        updateDashboardStats();
    }
    
    function deleteDocument(empId, docId) {
        showConfirm('تأكيد الحذف', 'هل أنت متأكد من حذف هذا المستند؟', () => {
            let allDocs = DB.getDocuments();
            if (allDocs[empId]) {
                allDocs[empId] = allDocs[empId].filter(doc => doc.docId !== docId);
                DB.saveDocuments(allDocs);
                showNotification('تم حذف المستند', 'success');
                showEmployeeDocs(empId);
                updateDashboardStats();
            }
        });
    }

    // --- FLEET MANAGEMENT ---
    function showFleetSection(searchTerm = '') {
        const vehicles = DB.getVehicles().filter(vehicle =>
            vehicle.plateNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
            vehicle.model.toLowerCase().includes(searchTerm.toLowerCase())
        );
        const employees = DB.getEmployees();

        const vehicleCards = vehicles.map(vehicle => {
            const assignedDriver = employees.find(emp => emp.id == vehicle.assignedDriver);
            const statusColor = {
                'available': 'status-active',
                'in_use': 'status-on_leave',
                'maintenance': 'status-inactive',
                'out_of_service': 'status-inactive'
            };
            const statusText = {
                'available': 'متاحة',
                'in_use': 'قيد الاستخدام',
                'maintenance': 'صيانة',
                'out_of_service': 'خارج الخدمة'
            };

            return `
                <div class="pro-card p-4 rounded-2xl">
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-lg font-bold text-white">${vehicle.plateNumber}</span>
                        <span class="status-badge ${statusColor[vehicle.status]}">${statusText[vehicle.status]}</span>
                    </div>
                    <div class="text-pink-300 text-sm space-y-2">
                        <div><i class="fas fa-car text-pink-400 w-5 mr-2"></i>${vehicle.model}</div>
                        <div><i class="fas fa-calendar text-green-400 w-5 mr-2"></i>${vehicle.year}</div>
                        <div><i class="fas fa-user text-rose-400 w-5 mr-2"></i>${assignedDriver ? assignedDriver.name : 'غير مخصصة'}</div>
                        <div><i class="fas fa-tools text-yellow-400 w-5 mr-2"></i>آخر صيانة: ${vehicle.lastMaintenance || 'لا توجد'}</div>
                    </div>
                    <div class="flex gap-2 mt-4">
                        <button onclick="showVehicleForm(${vehicle.id})" class="flex-1 pro-btn-outline text-xs py-2 rounded-md">تعديل</button>
                        <button onclick="deleteVehicle(${vehicle.id})" class="flex-1 pro-btn-outline text-xs py-2 rounded-md border-red-500/50 text-red-400">حذف</button>
                    </div>
                </div>`;
        }).join('');

        const content = `
            <div class="p-6 border-b border-white/20 sticky top-0 bg-slate-900/50 backdrop-blur-sm z-10">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-bold text-white">إدارة الأسطول</h2>
                    <button onclick="hideModal()" class="text-white hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button>
                </div>
                <div class="mt-4 flex gap-4">
                    <input type="text" id="vehicleSearchInput" onkeyup="showFleetSection(this.value)" value="${searchTerm}" placeholder="ابحث عن مركبة..." class="w-full form-input rounded-lg px-4 py-2">
                    <button onclick="showVehicleForm()" class="pro-btn px-6 py-2 rounded-lg text-white font-semibold whitespace-nowrap"><i class="fas fa-plus"></i> إضافة مركبة</button>
                </div>
            </div>
            <div class="p-6">
                <div id="vehicleList" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    ${vehicleCards || '<p class="text-slate-400 col-span-full text-center">لا توجد مركبات مسجلة.</p>'}
                </div>
            </div>`;
        showModal(content);
    }

    function showVehicleForm(id = null) {
        const isEditing = id !== null;
        const vehicle = isEditing ? DB.getVehicles().find(v => v.id === id) : {};
        const title = isEditing ? 'تعديل بيانات المركبة' : 'إضافة مركبة جديدة';
        const employees = DB.getEmployees().filter(emp => emp.job === 'driver');

        const driverOptions = employees.map(emp =>
            `<option value="${emp.id}" ${vehicle.assignedDriver == emp.id ? 'selected' : ''}>${emp.name}</option>`
        ).join('');

        const formHtml = `
            <div class="p-6 border-b border-white/20">
                <h2 class="text-2xl font-bold text-white text-center">${title}</h2>
            </div>
            <div class="p-8">
                <form id="vehicleForm" class="space-y-4">
                    <input type="hidden" name="id" value="${vehicle.id || ''}">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="text-slate-300 mb-2 block">رقم اللوحة</label>
                            <input type="text" name="plateNumber" value="${vehicle.plateNumber || ''}" class="w-full form-input rounded-lg px-4 py-2" required>
                        </div>
                        <div>
                            <label class="text-slate-300 mb-2 block">الموديل</label>
                            <input type="text" name="model" value="${vehicle.model || ''}" class="w-full form-input rounded-lg px-4 py-2" required>
                        </div>
                        <div>
                            <label class="text-slate-300 mb-2 block">سنة الصنع</label>
                            <input type="number" name="year" value="${vehicle.year || ''}" class="w-full form-input rounded-lg px-4 py-2" required>
                        </div>
                        <div>
                            <label class="text-slate-300 mb-2 block">الحالة</label>
                            <select name="status" class="w-full form-input rounded-lg px-4 py-2" required>
                                <option value="available" ${vehicle.status === 'available' ? 'selected' : ''}>متاحة</option>
                                <option value="in_use" ${vehicle.status === 'in_use' ? 'selected' : ''}>قيد الاستخدام</option>
                                <option value="maintenance" ${vehicle.status === 'maintenance' ? 'selected' : ''}>صيانة</option>
                                <option value="out_of_service" ${vehicle.status === 'out_of_service' ? 'selected' : ''}>خارج الخدمة</option>
                            </select>
                        </div>
                        <div>
                            <label class="text-slate-300 mb-2 block">السائق المخصص</label>
                            <select name="assignedDriver" class="w-full form-input rounded-lg px-4 py-2">
                                <option value="">غير مخصصة</option>
                                ${driverOptions}
                            </select>
                        </div>
                        <div>
                            <label class="text-slate-300 mb-2 block">تاريخ آخر صيانة</label>
                            <input type="date" name="lastMaintenance" value="${vehicle.lastMaintenance || ''}" class="w-full form-input rounded-lg px-4 py-2">
                        </div>
                    </div>
                    <div class="flex justify-end gap-4 pt-4">
                        <button type="button" onclick="showFleetSection()" class="pro-btn-outline px-6 py-2 rounded-lg font-semibold">إلغاء</button>
                        <button type="submit" class="pro-btn px-6 py-2 rounded-lg text-white font-semibold">حفظ البيانات</button>
                    </div>
                </form>
            </div>`;
        showModal(formHtml);
        document.getElementById('vehicleForm').addEventListener('submit', (e) => saveVehicle(e, isEditing));
    }

    function saveVehicle(event, isEditing) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const vehicleData = Object.fromEntries(formData.entries());
        let vehicles = DB.getVehicles();

        vehicleData.year = parseInt(vehicleData.year);
        vehicleData.assignedDriver = vehicleData.assignedDriver ? parseInt(vehicleData.assignedDriver) : null;

        if (isEditing) {
            const index = vehicles.findIndex(v => v.id == vehicleData.id);
            vehicles[index] = { ...vehicles[index], ...vehicleData };
            showNotification('تم تحديث بيانات المركبة بنجاح', 'success');
        } else {
            vehicleData.id = new Date().getTime();
            vehicles.push(vehicleData);
            showNotification('تمت إضافة المركبة بنجاح', 'success');
        }

        let employees = DB.getEmployees();
        employees.forEach(emp => {
            if (emp.vehicle === vehicleData.plateNumber && emp.id != vehicleData.assignedDriver) {
                emp.vehicle = 'N/A';
            }
        });

        if (vehicleData.assignedDriver) {
            const empIndex = employees.findIndex(emp => emp.id == vehicleData.assignedDriver);
            if (empIndex !== -1) {
                employees[empIndex].vehicle = vehicleData.plateNumber;
            }
        }

        DB.saveVehicles(vehicles);
        DB.saveEmployees(employees);
        updateDashboardStats();
        showFleetSection();
    }

    function deleteVehicle(id) {
        showConfirm('تأكيد الحذف', 'هل أنت متأكد من رغبتك في حذف هذه المركبة؟', () => {
            let vehicles = DB.getVehicles().filter(v => v.id !== id);
            DB.saveVehicles(vehicles);
            showNotification('تم حذف المركبة بنجاح', 'success');
            updateDashboardStats();
            showFleetSection();
        });
    }
    
    // --- EMPLOYEE PROFILE REPORT (NEW) ---
    function showEmployeeReportSearch(searchTerm = '') {
        const employees = DB.getEmployees().filter(emp => emp.name.toLowerCase().includes(searchTerm.toLowerCase()));
        const employeeButtons = employees.map(emp => `<button onclick="showEmployeeProfileReport(${emp.id})" class="pro-card p-4 text-center rounded-lg">${emp.name}</button>`).join('');
        
        const content = `
            <div class="p-6 border-b border-white/20 sticky top-0 bg-slate-900/50 backdrop-blur-sm z-10">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-bold text-white">بحث عن تقرير موظف</h2>
                    <button onclick="hideModal()" class="text-white hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button>
                </div>
                <div class="mt-4">
                    <input type="text" onkeyup="showEmployeeReportSearch(this.value)" value="${searchTerm}" placeholder="ابحث عن موظف لعرض تقريره..." class="w-full form-input rounded-lg px-4 py-2">
                </div>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    ${employeeButtons || `<p class="text-slate-400 col-span-full text-center">لم يتم العثور على موظفين.</p>`}
                </div>
            </div>`;
        showModal(content);
    }

    function showEmployeeProfileReport(empId) {
        const emp = DB.getEmployees().find(e => e.id === empId);
        if (!emp) return;

        const allDocs = DB.getDocuments()[empId] || [];
        const docTypes = { 
            civil_id: 'البطاقة المدنية', passport: 'جواز السفر', license: 'رخصة قيادة', 
            health_card: 'كرت الصحة', vehicle_registration: 'دفتر السيارة', contract: 'عقد عمل', other: 'أخرى' 
        };

        const docsList = allDocs.map(doc => {
            let statusBadge = '';
            if (doc.expiryDate) {
                const now = new Date();
                const expiry = new Date(doc.expiryDate);
                const daysDiff = (expiry - now) / (1000 * 60 * 60 * 24);
                if (daysDiff < 0) {
                    statusBadge = `<span class="status-badge expiry-expired">منتهي</span>`;
                } else if (daysDiff <= 30) {
                    statusBadge = `<span class="status-badge expiry-soon">ينتهي قريباً</span>`;
                } else {
                    statusBadge = `<span class="status-badge expiry-valid">صالح</span>`;
                }
            }
            return `
            <div class="pro-card p-3 rounded-lg flex justify-between items-center">
                <div>
                    <div class="font-semibold text-white">${docTypes[doc.type] || doc.type}</div>
                    <div class="text-sm text-slate-300">${doc.name}</div>
                    ${doc.expiryDate ? `<div class="text-sm text-slate-400">تاريخ الانتهاء: ${doc.expiryDate}</div>` : ''}
                </div>
                ${statusBadge}
            </div>`;
        }).join('');

        const content = `
        <div id="employeeProfileContent">
            <div class="p-6 border-b border-white/20 flex justify-between items-center">
                <div>
                    <h2 class="text-3xl font-bold text-white">ملف الموظف: ${emp.name}</h2>
                    <p class="text-pink-300">${emp.id} - ${emp.job === 'driver' ? 'سائق' : 'موظف'}</p>
                </div>
                <div class="no-print">
                    <button onclick="window.print()" class="pro-btn-outline px-4 py-2 rounded-lg mr-2"><i class="fas fa-print"></i> طباعة</button>
                    <button onclick="hideModal()" class="text-white hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button>
                </div>
            </div>
            <div class="p-8">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Documents Column -->
                    <div class="lg:col-span-2">
                        <h3 class="text-xl font-bold text-white mb-4 border-b border-slate-700 pb-2">المستندات الرسمية</h3>
                        <div class="space-y-4">
                            ${docsList || '<p class="text-slate-400">لا توجد مستندات مسجلة.</p>'}
                        </div>
                    </div>
                    <!-- Info Column -->
                    <div class="pro-card p-6 rounded-2xl">
                        <h3 class="text-xl font-bold text-white mb-4">بيانات أساسية</h3>
                        <div class="space-y-3 text-slate-200">
                            <div class="flex justify-between"><span>الهاتف:</span><span class="font-mono">${emp.phone}</span></div>
                            <div class="flex justify-between"><span>الراتب الأساسي:</span><span class="font-mono">${emp.salary} د.ك</span></div>
                            <div class="flex justify-between"><span>الحالة:</span><span>${emp.status === 'active' ? 'نشط' : 'غير نشط'}</span></div>
                            ${emp.job === 'driver' ? `<div class="flex justify-between"><span>المركبة:</span><span>${emp.vehicle || 'N/A'}</span></div>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        `;
        showModal(content);
    }
    
    // --- EXPIRING DOCUMENTS REPORT (NEW) ---
    function showExpiringDocumentsReport() {
        const allDocs = DB.getDocuments();
        const employees = DB.getEmployees();
        const docTypes = { 
            civil_id: 'البطاقة المدنية', passport: 'جواز السفر', license: 'رخصة قيادة', 
            health_card: 'كرت الصحة', vehicle_registration: 'دفتر السيارة', contract: 'عقد عمل', other: 'أخرى' 
        };
        
        let expiringDocs = [];
        const now = new Date();

        for (const empId in allDocs) {
            const emp = employees.find(e => e.id == empId);
            if (!emp) continue;

            allDocs[empId].forEach(doc => {
                if (doc.expiryDate) {
                    const expiry = new Date(doc.expiryDate);
                    const daysDiff = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24));
                    if (daysDiff <= 30) {
                        expiringDocs.push({ ...doc, empName: emp.name, daysDiff });
                    }
                }
            });
        }
        
        const docsList = expiringDocs
            .sort((a, b) => a.daysDiff - b.daysDiff)
            .map(doc => {
                let statusBadge;
                if (doc.daysDiff < 0) {
                    statusBadge = `<span class="status-badge expiry-expired">منتهي منذ ${Math.abs(doc.daysDiff)} يوم</span>`;
                } else {
                    statusBadge = `<span class="status-badge expiry-soon">ينتهي خلال ${doc.daysDiff} يوم</span>`;
                }

                return `
                <div class="pro-card p-3 rounded-lg flex justify-between items-center">
                    <div>
                        <div class="font-semibold text-white">${docTypes[doc.type] || doc.type} - ${doc.empName}</div>
                        <div class="text-sm text-slate-300">${doc.name} - ينتهي في: ${doc.expiryDate}</div>
                    </div>
                    ${statusBadge}
                </div>`;
        }).join('');

        const content = `
            <div class="p-6 border-b border-white/20 flex justify-between items-center">
                <h2 class="text-2xl font-bold text-white">تقرير المستندات قيد الانتهاء</h2>
                <button onclick="hideModal()" class="text-white hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button>
            </div>
            <div class="p-8">
                <div class="space-y-4">
                    ${docsList || '<p class="text-slate-400 text-center py-8">لا توجد مستندات منتهية أو على وشك الانتهاء.</p>'}
                </div>
            </div>
        `;
        showModal(content);
    }

    // --- NOTIFICATIONS MANAGEMENT ---
    function showNotificationsSection() {
        const content = `
            <div class="p-6 border-b border-white/20">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-bold text-white">الإشعارات والتنبيهات</h2>
                    <button onclick="hideModal()" class="text-white hover:text-red-400 text-2xl"><i class="fas fa-times"></i></button>
                </div>
            </div>
            <div class="p-8">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="pro-card p-6 rounded-2xl">
                        <h3 class="text-xl font-bold text-white mb-4"><i class="fas fa-bell text-rose-400 mr-2"></i>إرسال إشعار جماعي</h3>
                        <form id="broadcastForm" class="space-y-4">
                            <input type="text" name="title" placeholder="عنوان الإشعار" class="w-full form-input rounded-lg px-4 py-2" required>
                            <textarea name="message" placeholder="نص الرسالة..." class="w-full form-input rounded-lg px-4 py-2 h-24" required></textarea>
                            <select name="priority" class="w-full form-input rounded-lg px-4 py-2">
                                <option value="normal">عادي</option>
                                <option value="high">مهم</option>
                                <option value="urgent">عاجل</option>
                            </select>
                            <button type="submit" class="pro-btn w-full py-2 rounded-lg">إرسال للجميع</button>
                        </form>
                    </div>
                    <div class="pro-card p-6 rounded-2xl">
                        <h3 class="text-xl font-bold text-white mb-4"><i class="fas fa-tools text-yellow-400 mr-2"></i>تنبيهات تلقائية</h3>
                        <div id="auto-alerts" class="space-y-3">
                           <!-- Alerts will be generated here -->
                        </div>
                    </div>
                </div>
            </div>`;
        showModal(content);

        // Populate auto-alerts
        const allDocs = DB.getDocuments();
        const now = new Date();
        let alertsHtml = '';
        for (const empId in allDocs) {
            const emp = DB.getEmployees().find(e => e.id == empId);
            if (!emp) continue;
            allDocs[empId].forEach(doc => {
                if (doc.expiryDate) {
                    const expiry = new Date(doc.expiryDate);
                    const daysDiff = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24));
                    if (daysDiff <= 30 && daysDiff > 0) {
                        alertsHtml += `
                        <div class="bg-slate-800/50 p-3 rounded-lg">
                            <div class="text-yellow-400 font-semibold">${doc.name} للموظف ${emp.name}</div>
                            <div class="text-slate-300 text-sm">ينتهي خلال ${daysDiff} يوم.</div>
                        </div>`;
                    }
                }
            });
        }
        document.getElementById('auto-alerts').innerHTML = alertsHtml || '<p class="text-slate-400">لا توجد تنبيهات حال.<p class="text-slate-400">لا توجد تنبيهات حال.</p>';


        document.getElementById('broadcastForm').addEventListener('submit', (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const title = formData.get('title');
            const message = formData.get('message');
            showNotification(`تم إرسال إشعار: ${title}`, 'success');
            e.target.reset();
        });
    }
  </script>
</body>
</html>

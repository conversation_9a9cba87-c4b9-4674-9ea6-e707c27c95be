using System.ComponentModel.DataAnnotations;

namespace ManagementSystem.Models.ViewModels
{
    public class UserListViewModel
    {
        public IEnumerable<UserItemViewModel> Users { get; set; } = new List<UserItemViewModel>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SearchTerm { get; set; }
        public string? Department { get; set; }
        public bool? IsActive { get; set; }
        public string SortBy { get; set; } = "FirstName";
        public string SortDirection { get; set; } = "asc";

        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }

    public class UserItemViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FullName => $"{FirstName} {LastName}";
        public string Email { get; set; } = string.Empty;
        public string? Department { get; set; }
        public string? Position { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public IEnumerable<string> Roles { get; set; } = new List<string>();
    }

    public class UserDetailsViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FullName => $"{FirstName} {LastName}";
        public string Email { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? Department { get; set; }
        public string? Position { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public IEnumerable<RoleItemViewModel> Roles { get; set; } = new List<RoleItemViewModel>();
        public IEnumerable<AuditLogItemViewModel> RecentActivities { get; set; } = new List<AuditLogItemViewModel>();
    }

    public class CreateUserViewModel
    {
        [Required(ErrorMessage = "الاسم الأول مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم الأول يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "الاسم الأول")]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الأخير مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم الأخير يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "الاسم الأخير")]
        public string LastName { get; set; } = string.Empty;

        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = string.Empty;

        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        [Display(Name = "رقم الهاتف")]
        public string? PhoneNumber { get; set; }

        [StringLength(200, ErrorMessage = "القسم يجب أن يكون أقل من 200 حرف")]
        [Display(Name = "القسم")]
        public string? Department { get; set; }

        [StringLength(200, ErrorMessage = "المنصب يجب أن يكون أقل من 200 حرف")]
        [Display(Name = "المنصب")]
        public string? Position { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "الأدوار")]
        public List<string> SelectedRoles { get; set; } = new List<string>();

        public IEnumerable<RoleItemViewModel> AvailableRoles { get; set; } = new List<RoleItemViewModel>();

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [StringLength(100, ErrorMessage = "كلمة المرور يجب أن تكون على الأقل {2} حرف وأقل من {1} حرف.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "كلمة المرور")]
        public string Password { get; set; } = string.Empty;

        [DataType(DataType.Password)]
        [Display(Name = "تأكيد كلمة المرور")]
        [Compare("Password", ErrorMessage = "كلمة المرور وتأكيد كلمة المرور غير متطابقتين")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    public class EditUserViewModel
    {
        public string Id { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الأول مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم الأول يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "الاسم الأول")]
        public string FirstName { get; set; } = string.Empty;

        [Required(ErrorMessage = "الاسم الأخير مطلوب")]
        [StringLength(100, ErrorMessage = "الاسم الأخير يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "الاسم الأخير")]
        public string LastName { get; set; } = string.Empty;

        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = string.Empty;

        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        [Display(Name = "رقم الهاتف")]
        public string? PhoneNumber { get; set; }

        [StringLength(200, ErrorMessage = "القسم يجب أن يكون أقل من 200 حرف")]
        [Display(Name = "القسم")]
        public string? Department { get; set; }

        [StringLength(200, ErrorMessage = "المنصب يجب أن يكون أقل من 200 حرف")]
        [Display(Name = "المنصب")]
        public string? Position { get; set; }

        [Display(Name = "نشط")]
        public bool IsActive { get; set; }

        [Display(Name = "الأدوار")]
        public List<string> SelectedRoles { get; set; } = new List<string>();

        public IEnumerable<RoleItemViewModel> AvailableRoles { get; set; } = new List<RoleItemViewModel>();
    }

    public class RoleItemViewModel
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsSelected { get; set; }
    }

    public class AuditLogItemViewModel
    {
        public int Id { get; set; }
        public string Action { get; set; } = string.Empty;
        public string EntityName { get; set; } = string.Empty;
        public string? EntityId { get; set; }
        public DateTime CreatedAt { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string? IpAddress { get; set; }
    }
}

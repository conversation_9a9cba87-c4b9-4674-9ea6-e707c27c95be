# نظام الإدارة - Management System

نظام إدارة شامل مبني باستخدام ASP.NET Core MVC مع دعم كامل للغة العربية وواجهة مستخدم حديثة.

## المميزات الرئيسية

### 🔐 نظام التوثيق والأمان
- تسجيل الدخول والخروج الآمن
- إدارة كلمات المرور مع إعادة التعيين
- نظام الأدوار والصلاحيات (Role-Based Access Control)
- تسجيل جميع العمليات (Audit Logging)

### 👥 إدارة المستخدمين
- إضافة وتعديل وحذف المستخدمين
- تفعيل وتعطيل الحسابات
- تخصيص الأدوار والصلاحيات
- البحث والتصفية المتقدم

### 📊 لوحة التحكم
- إحصائيات شاملة عن النظام
- عرض الأنشطة الأخيرة
- إجراءات سريعة للعمليات الشائعة

### 📧 نظام الإشعارات
- إشعارات عبر البريد الإلكتروني
- تنبيهات تلقائية للعمليات المهمة
- رسائل ترحيب للمستخدمين الجدد

### 📝 تسجيل العمليات
- تسجيل تفصيلي لجميع العمليات
- تتبع التغييرات مع القيم القديمة والجديدة
- معلومات المستخدم وعنوان IP

## التقنيات المستخدمة

| المكون | التقنية |
|--------|---------|
| إطار العمل | ASP.NET Core 9.0 MVC |
| قاعدة البيانات | SQL Server مع Entity Framework Core |
| التوثيق | ASP.NET Core Identity |
| واجهة المستخدم | Tailwind CSS + Font Awesome |
| التسجيل | Serilog |
| البريد الإلكتروني | SMTP |

## متطلبات التشغيل

- .NET 9.0 SDK أو أحدث
- SQL Server 2019 أو أحدث (أو SQL Server LocalDB)
- Visual Studio 2022 أو Visual Studio Code

## التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd ManagementSystem
```

### 2. تكوين قاعدة البيانات
قم بتحديث connection string في `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=ManagementSystemDb;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

### 3. تثبيت الحزم
```bash
dotnet restore
```

### 4. إنشاء قاعدة البيانات
```bash
dotnet ef database update
```

### 5. تشغيل التطبيق
```bash
dotnet run
```

## بيانات تسجيل الدخول الافتراضية

**المدير:**
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `Admin123!`

## تكوين البريد الإلكتروني

لتفعيل إرسال الإشعارات عبر البريد الإلكتروني، قم بتحديث إعدادات البريد في `appsettings.json`:

```json
{
  "EmailSettings": {
    "Enabled": true,
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "EnableSsl": true,
    "Username": "<EMAIL>",
    "Password": "your-app-password",
    "FromEmail": "<EMAIL>",
    "FromName": "نظام الإدارة"
  }
}
```

## هيكل المشروع

```
ManagementSystem/
├── Controllers/          # وحدات التحكم
├── Models/              # النماذج
│   ├── ViewModels/      # نماذج العرض
│   └── ...
├── Views/               # واجهات المستخدم
│   ├── Auth/           # صفحات التوثيق
│   ├── Dashboard/      # لوحة التحكم
│   ├── Users/          # إدارة المستخدمين
│   └── Shared/         # القوالب المشتركة
├── Services/            # الخدمات
├── Data/               # سياق قاعدة البيانات
├── wwwroot/            # الملفات الثابتة
└── logs/               # ملفات السجلات
```

## الأدوار والصلاحيات

### Admin (مدير النظام)
- جميع الصلاحيات
- إدارة المستخدمين والأدوار
- عرض التقارير والسجلات
- إعدادات النظام

### Manager (مدير)
- عرض وإنشاء وتعديل المستخدمين
- عرض التقارير
- عرض الأدوار

### User (مستخدم)
- عرض المستخدمين فقط

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى البranch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى إنشاء issue في المستودع.

## الإصدارات القادمة

- [ ] نظام التقارير المتقدم
- [ ] إدارة الملفات والمرفقات
- [ ] نظام الإشعارات الفورية
- [ ] API للتطبيقات الخارجية
- [ ] دعم التوثيق الثنائي (2FA)
- [ ] نظام النسخ الاحتياطي التلقائي

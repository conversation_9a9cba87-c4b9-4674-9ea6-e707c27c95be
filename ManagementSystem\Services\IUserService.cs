using ManagementSystem.Data;
using ManagementSystem.Models;
using ManagementSystem.Models.ViewModels;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace ManagementSystem.Services
{
    public interface IUserService
    {
        Task<UserListViewModel> GetUsersAsync(int pageNumber = 1, int pageSize = 10, string? searchTerm = null, string? department = null, bool? isActive = null, string sortBy = "FirstName", string sortDirection = "asc");
        Task<UserDetailsViewModel?> GetUserDetailsAsync(string userId);
        Task<IdentityResult> CreateUserAsync(CreateUserViewModel model, string createdByUserId);
        Task<IdentityResult> UpdateUserAsync(EditUserViewModel model, string updatedByUserId);
        Task<IdentityResult> DeleteUserAsync(string userId, string deletedByUserId);
        Task<IdentityResult> ToggleUserStatusAsync(string userId, string updatedByUserId);
        Task<IEnumerable<RoleItemViewModel>> GetAvailableRolesAsync();
        Task<IEnumerable<string>> GetDepartmentsAsync();
    }

    public class UserService : IUserService
    {
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<Role> _roleManager;
        private readonly ApplicationDbContext _context;
        private readonly IAuditService _auditService;

        public UserService(
            UserManager<User> userManager,
            RoleManager<Role> roleManager,
            ApplicationDbContext context,
            IAuditService auditService)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _context = context;
            _auditService = auditService;
        }

        public async Task<UserListViewModel> GetUsersAsync(
            int pageNumber = 1, 
            int pageSize = 10, 
            string? searchTerm = null, 
            string? department = null, 
            bool? isActive = null, 
            string sortBy = "FirstName", 
            string sortDirection = "asc")
        {
            var query = _context.Users.AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(u => u.FirstName.Contains(searchTerm) || 
                                        u.LastName.Contains(searchTerm) || 
                                        u.Email.Contains(searchTerm));
            }

            if (!string.IsNullOrEmpty(department))
            {
                query = query.Where(u => u.Department == department);
            }

            if (isActive.HasValue)
            {
                query = query.Where(u => u.IsActive == isActive.Value);
            }

            // Apply sorting
            query = sortBy.ToLower() switch
            {
                "lastname" => sortDirection == "desc" ? query.OrderByDescending(u => u.LastName) : query.OrderBy(u => u.LastName),
                "email" => sortDirection == "desc" ? query.OrderByDescending(u => u.Email) : query.OrderBy(u => u.Email),
                "department" => sortDirection == "desc" ? query.OrderByDescending(u => u.Department) : query.OrderBy(u => u.Department),
                "createdat" => sortDirection == "desc" ? query.OrderByDescending(u => u.CreatedAt) : query.OrderBy(u => u.CreatedAt),
                "lastloginat" => sortDirection == "desc" ? query.OrderByDescending(u => u.LastLoginAt) : query.OrderBy(u => u.LastLoginAt),
                _ => sortDirection == "desc" ? query.OrderByDescending(u => u.FirstName) : query.OrderBy(u => u.FirstName)
            };

            var totalCount = await query.CountAsync();

            var users = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Select(u => new UserItemViewModel
                {
                    Id = u.Id,
                    FirstName = u.FirstName,
                    LastName = u.LastName,
                    Email = u.Email,
                    Department = u.Department,
                    Position = u.Position,
                    IsActive = u.IsActive,
                    CreatedAt = u.CreatedAt,
                    LastLoginAt = u.LastLoginAt
                })
                .ToListAsync();

            // Get roles for each user
            foreach (var user in users)
            {
                var userEntity = await _userManager.FindByIdAsync(user.Id);
                if (userEntity != null)
                {
                    user.Roles = await _userManager.GetRolesAsync(userEntity);
                }
            }

            return new UserListViewModel
            {
                Users = users,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize,
                SearchTerm = searchTerm,
                Department = department,
                IsActive = isActive,
                SortBy = sortBy,
                SortDirection = sortDirection
            };
        }

        public async Task<UserDetailsViewModel?> GetUserDetailsAsync(string userId)
        {
            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Id == userId);

            if (user == null) return null;

            var roles = await _userManager.GetRolesAsync(user);
            var roleViewModels = new List<RoleItemViewModel>();

            foreach (var roleName in roles)
            {
                var role = await _roleManager.FindByNameAsync(roleName);
                if (role != null)
                {
                    roleViewModels.Add(new RoleItemViewModel
                    {
                        Id = role.Id,
                        Name = role.Name ?? string.Empty,
                        Description = role.Description
                    });
                }
            }

            var recentActivities = await _auditService.GetRecentActivitiesAsync(userId, 10);

            return new UserDetailsViewModel
            {
                Id = user.Id,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber,
                Department = user.Department,
                Position = user.Position,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                LastLoginAt = user.LastLoginAt,
                Roles = roleViewModels,
                RecentActivities = recentActivities
            };
        }

        public async Task<IdentityResult> CreateUserAsync(CreateUserViewModel model, string createdByUserId)
        {
            var user = new User
            {
                UserName = model.Email,
                Email = model.Email,
                FirstName = model.FirstName,
                LastName = model.LastName,
                PhoneNumber = model.PhoneNumber,
                Department = model.Department,
                Position = model.Position,
                IsActive = model.IsActive,
                CreatedAt = DateTime.UtcNow
            };

            var result = await _userManager.CreateAsync(user, model.Password);

            if (result.Succeeded)
            {
                // Add roles
                if (model.SelectedRoles.Any())
                {
                    await _userManager.AddToRolesAsync(user, model.SelectedRoles);
                }

                // Log audit
                var newValues = System.Text.Json.JsonSerializer.Serialize(new
                {
                    user.Email,
                    user.FirstName,
                    user.LastName,
                    user.Department,
                    user.Position,
                    user.IsActive,
                    Roles = model.SelectedRoles
                });

                await _auditService.LogAsync("CreateUser", "User", user.Id, null, newValues, createdByUserId);
            }

            return result;
        }

        public async Task<IdentityResult> UpdateUserAsync(EditUserViewModel model, string updatedByUserId)
        {
            var user = await _userManager.FindByIdAsync(model.Id);
            if (user == null)
            {
                return IdentityResult.Failed(new IdentityError { Description = "المستخدم غير موجود" });
            }

            // Store old values for audit
            var oldValues = System.Text.Json.JsonSerializer.Serialize(new
            {
                user.Email,
                user.FirstName,
                user.LastName,
                user.Department,
                user.Position,
                user.IsActive,
                Roles = await _userManager.GetRolesAsync(user)
            });

            // Update user properties
            user.Email = model.Email;
            user.UserName = model.Email;
            user.FirstName = model.FirstName;
            user.LastName = model.LastName;
            user.PhoneNumber = model.PhoneNumber;
            user.Department = model.Department;
            user.Position = model.Position;
            user.IsActive = model.IsActive;

            var result = await _userManager.UpdateAsync(user);

            if (result.Succeeded)
            {
                // Update roles
                var currentRoles = await _userManager.GetRolesAsync(user);
                var rolesToRemove = currentRoles.Except(model.SelectedRoles);
                var rolesToAdd = model.SelectedRoles.Except(currentRoles);

                if (rolesToRemove.Any())
                {
                    await _userManager.RemoveFromRolesAsync(user, rolesToRemove);
                }

                if (rolesToAdd.Any())
                {
                    await _userManager.AddToRolesAsync(user, rolesToAdd);
                }

                // Log audit
                var newValues = System.Text.Json.JsonSerializer.Serialize(new
                {
                    user.Email,
                    user.FirstName,
                    user.LastName,
                    user.Department,
                    user.Position,
                    user.IsActive,
                    Roles = model.SelectedRoles
                });

                await _auditService.LogAsync("UpdateUser", "User", user.Id, oldValues, newValues, updatedByUserId);
            }

            return result;
        }

        public async Task<IdentityResult> DeleteUserAsync(string userId, string deletedByUserId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return IdentityResult.Failed(new IdentityError { Description = "المستخدم غير موجود" });
            }

            // Store old values for audit
            var oldValues = System.Text.Json.JsonSerializer.Serialize(new
            {
                user.Email,
                user.FirstName,
                user.LastName,
                user.Department,
                user.Position,
                user.IsActive
            });

            var result = await _userManager.DeleteAsync(user);

            if (result.Succeeded)
            {
                await _auditService.LogAsync("DeleteUser", "User", userId, oldValues, null, deletedByUserId);
            }

            return result;
        }

        public async Task<IdentityResult> ToggleUserStatusAsync(string userId, string updatedByUserId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return IdentityResult.Failed(new IdentityError { Description = "المستخدم غير موجود" });
            }

            var oldStatus = user.IsActive;
            user.IsActive = !user.IsActive;

            var result = await _userManager.UpdateAsync(user);

            if (result.Succeeded)
            {
                var action = user.IsActive ? "ActivateUser" : "DeactivateUser";
                await _auditService.LogAsync(action, "User", userId, 
                    $"{{\"IsActive\":{oldStatus.ToString().ToLower()}}}", 
                    $"{{\"IsActive\":{user.IsActive.ToString().ToLower()}}}", 
                    updatedByUserId);
            }

            return result;
        }

        public async Task<IEnumerable<RoleItemViewModel>> GetAvailableRolesAsync()
        {
            return await _context.Roles
                .Where(r => r.IsActive)
                .Select(r => new RoleItemViewModel
                {
                    Id = r.Id,
                    Name = r.Name ?? string.Empty,
                    Description = r.Description
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<string>> GetDepartmentsAsync()
        {
            return await _context.Users
                .Where(u => !string.IsNullOrEmpty(u.Department))
                .Select(u => u.Department!)
                .Distinct()
                .OrderBy(d => d)
                .ToListAsync();
        }
    }
}

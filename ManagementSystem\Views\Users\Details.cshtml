@model ManagementSystem.Models.ViewModels.UserDetailsViewModel
@{
    ViewData["Title"] = $"تفاصيل المستخدم - {Model.FullName}";
    Layout = "_LayoutNew";
}

<div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="h-12 w-12 rounded-full @(Model.IsActive ? "bg-green-500" : "bg-gray-400") flex items-center justify-center ml-4">
                    <span class="text-white text-lg font-medium">@Model.FirstName.Substring(0, 1)@Model.LastName.Substring(0, 1)</span>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">@Model.FullName</h1>
                    <div class="flex items-center mt-1">
                        <p class="text-sm text-gray-600">@Model.Email</p>
                        @if (!Model.IsActive)
                        {
                            <span class="mr-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                غير نشط
                            </span>
                        }
                        else
                        {
                            <span class="mr-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                نشط
                            </span>
                        }
                    </div>
                </div>
            </div>
            <div class="flex items-center space-x-3 space-x-reverse">
                <a href="@Url.Action("Edit", "Users", new { id = Model.Id })" 
                   class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-edit ml-2"></i>
                    تعديل
                </a>
                <a href="@Url.Action("Index", "Users")" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة إلى القائمة
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- User Information -->
        <div class="lg:col-span-2">
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">معلومات المستخدم</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">التفاصيل الشخصية ومعلومات الحساب</p>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">الاسم الكامل</dt>
                            <dd class="mt-1 text-sm text-gray-900">@Model.FullName</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500">البريد الإلكتروني</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                <a href="mailto:@Model.Email" class="text-blue-600 hover:text-blue-500">@Model.Email</a>
                            </dd>
                        </div>

                        @if (!string.IsNullOrEmpty(Model.PhoneNumber))
                        {
                            <div>
                                <dt class="text-sm font-medium text-gray-500">رقم الهاتف</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    <a href="tel:@Model.PhoneNumber" class="text-blue-600 hover:text-blue-500">@Model.PhoneNumber</a>
                                </dd>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(Model.Department))
                        {
                            <div>
                                <dt class="text-sm font-medium text-gray-500">القسم</dt>
                                <dd class="mt-1 text-sm text-gray-900">@Model.Department</dd>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(Model.Position))
                        {
                            <div>
                                <dt class="text-sm font-medium text-gray-500">المنصب</dt>
                                <dd class="mt-1 text-sm text-gray-900">@Model.Position</dd>
                            </div>
                        }

                        <div>
                            <dt class="text-sm font-medium text-gray-500">حالة الحساب</dt>
                            <dd class="mt-1">
                                @if (Model.IsActive)
                                {
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle ml-1"></i>
                                        نشط
                                    </span>
                                }
                                else
                                {
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fas fa-times-circle ml-1"></i>
                                        غير نشط
                                    </span>
                                }
                            </dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500">تاريخ الانضمام</dt>
                            <dd class="mt-1 text-sm text-gray-900">@Model.CreatedAt.ToString("yyyy/MM/dd HH:mm")</dd>
                        </div>

                        <div>
                            <dt class="text-sm font-medium text-gray-500">آخر تسجيل دخول</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                @if (Model.LastLoginAt.HasValue)
                                {
                                    @Model.LastLoginAt.Value.ToString("yyyy/MM/dd HH:mm")
                                }
                                else
                                {
                                    <span class="text-gray-400">لم يسجل دخول بعد</span>
                                }
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Roles -->
            <div class="bg-white shadow rounded-lg mt-6">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">الأدوار والصلاحيات</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">الأدوار المخصصة لهذا المستخدم</p>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    @if (Model.Roles.Any())
                    {
                        <div class="flex flex-wrap gap-2">
                            @foreach (var role in Model.Roles)
                            {
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-user-tag ml-1"></i>
                                    @role.Name
                                </span>
                            }
                        </div>
                        <div class="mt-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">وصف الأدوار:</h4>
                            <ul class="space-y-1">
                                @foreach (var role in Model.Roles.Where(r => !string.IsNullOrEmpty(r.Description)))
                                {
                                    <li class="text-sm text-gray-600">
                                        <strong>@role.Name:</strong> @role.Description
                                    </li>
                                }
                            </ul>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-6">
                            <i class="fas fa-user-tag text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-500">لم يتم تخصيص أي أدوار لهذا المستخدم</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">إجراءات سريعة</h3>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <div class="space-y-3">
                        <a href="@Url.Action("Edit", "Users", new { id = Model.Id })" 
                           class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-edit ml-2"></i>
                            تعديل المستخدم
                        </a>

                        <form asp-action="ToggleStatus" asp-route-id="@Model.Id" method="post" class="w-full">
                            <button type="submit" 
                                    class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white @(Model.IsActive ? "bg-orange-600 hover:bg-orange-700" : "bg-green-600 hover:bg-green-700") focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                                    onclick="return confirm('@(Model.IsActive ? "هل أنت متأكد من تعطيل هذا المستخدم؟" : "هل أنت متأكد من تفعيل هذا المستخدم؟")')">
                                <i class="fas @(Model.IsActive ? "fa-user-slash" : "fa-user-check") ml-2"></i>
                                @(Model.IsActive ? "تعطيل الحساب" : "تفعيل الحساب")
                            </button>
                        </form>

                        <button type="button" 
                                class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                onclick="resetPassword('@Model.Id')">
                            <i class="fas fa-key ml-2"></i>
                            إعادة تعيين كلمة المرور
                        </button>

                        <form asp-action="Delete" asp-route-id="@Model.Id" method="post" class="w-full">
                            <button type="submit" 
                                    class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                    onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                <i class="fas fa-trash ml-2"></i>
                                حذف المستخدم
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="bg-white shadow rounded-lg mt-6">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">الأنشطة الأخيرة</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">آخر 10 عمليات</p>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    @if (Model.RecentActivities.Any())
                    {
                        <div class="flow-root">
                            <ul class="-mb-8">
                                @foreach (var activity in Model.RecentActivities.Take(5))
                                {
                                    <li class="relative pb-8">
                                        <div class="relative flex space-x-3 space-x-reverse">
                                            <div>
                                                @{
                                                    var iconClass = activity.Action switch
                                                    {
                                                        "Login" => "fas fa-sign-in-alt text-green-500",
                                                        "Logout" => "fas fa-sign-out-alt text-gray-500",
                                                        "UpdateUser" => "fas fa-user-edit text-yellow-500",
                                                        "ChangePassword" => "fas fa-key text-blue-500",
                                                        _ => "fas fa-history text-gray-500"
                                                    };
                                                }
                                                <span class="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center ring-8 ring-white">
                                                    <i class="@iconClass text-sm"></i>
                                                </span>
                                            </div>
                                            <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4 space-x-reverse">
                                                <div>
                                                    <p class="text-sm text-gray-500">
                                                        @{
                                                            var actionText = activity.Action switch
                                                            {
                                                                "Login" => "سجل دخول",
                                                                "Logout" => "سجل خروج",
                                                                "UpdateUser" => "تم تحديث المعلومات",
                                                                "ChangePassword" => "غير كلمة المرور",
                                                                _ => activity.Action
                                                            };
                                                        }
                                                        @actionText
                                                    </p>
                                                </div>
                                                <div class="text-left text-sm whitespace-nowrap text-gray-500">
                                                    <time datetime="@activity.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss")">
                                                        @activity.CreatedAt.ToString("MM/dd HH:mm")
                                                    </time>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                }
                            </ul>
                        </div>
                        @if (Model.RecentActivities.Count() > 5)
                        {
                            <div class="mt-4">
                                <a href="@Url.Action("Index", "AuditLogs", new { userId = Model.Id })" 
                                   class="text-sm font-medium text-blue-600 hover:text-blue-500">
                                    عرض جميع الأنشطة
                                    <i class="fas fa-arrow-left mr-1"></i>
                                </a>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-6">
                            <i class="fas fa-history text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-500 text-sm">لا توجد أنشطة حديثة</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function resetPassword(userId) {
            if (confirm('هل أنت متأكد من إعادة تعيين كلمة المرور؟ سيتم إرسال كلمة مرور جديدة عبر البريد الإلكتروني.')) {
                // In a real application, you would make an AJAX call to reset the password
                alert('تم إرسال كلمة المرور الجديدة عبر البريد الإلكتروني');
            }
        }
    </script>
}

using ManagementSystem.Data;
using ManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace ManagementSystem.Services
{
    public interface INotificationService
    {
        Task SendUserCreatedNotificationAsync(string userId, string createdByUserId);
        Task SendUserUpdatedNotificationAsync(string userId, string updatedByUserId);
        Task SendUserDeletedNotificationAsync(string userEmail, string userName, string deletedByUserId);
        Task SendUserStatusChangedNotificationAsync(string userId, bool isActive, string updatedByUserId);
        Task SendPasswordChangedNotificationAsync(string userId);
        Task SendLoginNotificationAsync(string userId, string ipAddress);
        Task SendSystemAlertAsync(string message, string level = "info");
    }

    public class NotificationService : INotificationService
    {
        private readonly IEmailService _emailService;
        private readonly ApplicationDbContext _context;
        private readonly ILogger<NotificationService> _logger;

        public NotificationService(
            IEmailService emailService,
            ApplicationDbContext context,
            ILogger<NotificationService> logger)
        {
            _emailService = emailService;
            _context = context;
            _logger = logger;
        }

        public async Task SendUserCreatedNotificationAsync(string userId, string createdByUserId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                var createdBy = await _context.Users.FindAsync(createdByUserId);

                if (user == null || createdBy == null) return;

                // Send welcome email to the new user
                var tempPassword = "TempPass123!"; // In real scenario, this would be generated
                await _emailService.SendWelcomeEmailAsync(user.Email, $"{user.FirstName} {user.LastName}", tempPassword);

                // Send notification to admins
                var adminEmails = await GetAdminEmailsAsync();
                if (adminEmails.Any())
                {
                    var subject = "تم إنشاء مستخدم جديد - نظام الإدارة";
                    var body = $@"
                        <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
                            <h3>تم إنشاء مستخدم جديد</h3>
                            <p><strong>المستخدم الجديد:</strong> {user.FirstName} {user.LastName}</p>
                            <p><strong>البريد الإلكتروني:</strong> {user.Email}</p>
                            <p><strong>القسم:</strong> {user.Department ?? "غير محدد"}</p>
                            <p><strong>المنصب:</strong> {user.Position ?? "غير محدد"}</p>
                            <p><strong>تم الإنشاء بواسطة:</strong> {createdBy.FirstName} {createdBy.LastName}</p>
                            <p><strong>التاريخ:</strong> {DateTime.Now:yyyy/MM/dd HH:mm}</p>
                        </div>";

                    await _emailService.SendEmailAsync(adminEmails, subject, body);
                }

                _logger.LogInformation("User creation notification sent for user {UserId}", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send user creation notification for user {UserId}", userId);
            }
        }

        public async Task SendUserUpdatedNotificationAsync(string userId, string updatedByUserId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                var updatedBy = await _context.Users.FindAsync(updatedByUserId);

                if (user == null || updatedBy == null) return;

                // Send notification to the user
                var subject = "تم تحديث معلومات حسابك - نظام الإدارة";
                var body = $@"
                    <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
                        <h3>تم تحديث معلومات حسابك</h3>
                        <p>عزيزي {user.FirstName} {user.LastName}،</p>
                        <p>تم تحديث معلومات حسابك في نظام الإدارة.</p>
                        <p><strong>تم التحديث بواسطة:</strong> {updatedBy.FirstName} {updatedBy.LastName}</p>
                        <p><strong>التاريخ:</strong> {DateTime.Now:yyyy/MM/dd HH:mm}</p>
                        <p>إذا لم تطلب هذا التحديث، يرجى التواصل مع المدير فوراً.</p>
                    </div>";

                await _emailService.SendEmailAsync(user.Email, subject, body);

                _logger.LogInformation("User update notification sent for user {UserId}", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send user update notification for user {UserId}", userId);
            }
        }

        public async Task SendUserDeletedNotificationAsync(string userEmail, string userName, string deletedByUserId)
        {
            try
            {
                var deletedBy = await _context.Users.FindAsync(deletedByUserId);
                if (deletedBy == null) return;

                // Send notification to admins
                var adminEmails = await GetAdminEmailsAsync();
                if (adminEmails.Any())
                {
                    var subject = "تم حذف مستخدم - نظام الإدارة";
                    var body = $@"
                        <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
                            <h3 style='color: #dc3545;'>تم حذف مستخدم</h3>
                            <p><strong>المستخدم المحذوف:</strong> {userName}</p>
                            <p><strong>البريد الإلكتروني:</strong> {userEmail}</p>
                            <p><strong>تم الحذف بواسطة:</strong> {deletedBy.FirstName} {deletedBy.LastName}</p>
                            <p><strong>التاريخ:</strong> {DateTime.Now:yyyy/MM/dd HH:mm}</p>
                        </div>";

                    await _emailService.SendEmailAsync(adminEmails, subject, body);
                }

                _logger.LogInformation("User deletion notification sent for user {UserEmail}", userEmail);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send user deletion notification for user {UserEmail}", userEmail);
            }
        }

        public async Task SendUserStatusChangedNotificationAsync(string userId, bool isActive, string updatedByUserId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                var updatedBy = await _context.Users.FindAsync(updatedByUserId);

                if (user == null || updatedBy == null) return;

                // Send notification to the user
                await _emailService.SendAccountStatusChangeEmailAsync(user.Email, $"{user.FirstName} {user.LastName}", isActive);

                // Send notification to admins
                var adminEmails = await GetAdminEmailsAsync();
                if (adminEmails.Any())
                {
                    var statusText = isActive ? "تفعيل" : "تعطيل";
                    var subject = $"تم {statusText} مستخدم - نظام الإدارة";
                    var body = $@"
                        <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
                            <h3>تم {statusText} مستخدم</h3>
                            <p><strong>المستخدم:</strong> {user.FirstName} {user.LastName}</p>
                            <p><strong>البريد الإلكتروني:</strong> {user.Email}</p>
                            <p><strong>الحالة الجديدة:</strong> {(isActive ? "نشط" : "غير نشط")}</p>
                            <p><strong>تم التحديث بواسطة:</strong> {updatedBy.FirstName} {updatedBy.LastName}</p>
                            <p><strong>التاريخ:</strong> {DateTime.Now:yyyy/MM/dd HH:mm}</p>
                        </div>";

                    await _emailService.SendEmailAsync(adminEmails, subject, body);
                }

                _logger.LogInformation("User status change notification sent for user {UserId}", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send user status change notification for user {UserId}", userId);
            }
        }

        public async Task SendPasswordChangedNotificationAsync(string userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null) return;

                var subject = "تم تغيير كلمة المرور - نظام الإدارة";
                var body = $@"
                    <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
                        <h3>تم تغيير كلمة المرور</h3>
                        <p>عزيزي {user.FirstName} {user.LastName}،</p>
                        <p>تم تغيير كلمة المرور لحسابك في نظام الإدارة بنجاح.</p>
                        <p><strong>التاريخ:</strong> {DateTime.Now:yyyy/MM/dd HH:mm}</p>
                        <p>إذا لم تقم بهذا التغيير، يرجى التواصل مع المدير فوراً.</p>
                    </div>";

                await _emailService.SendEmailAsync(user.Email, subject, body);

                _logger.LogInformation("Password change notification sent for user {UserId}", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send password change notification for user {UserId}", userId);
            }
        }

        public async Task SendLoginNotificationAsync(string userId, string ipAddress)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null) return;

                // Only send login notifications for suspicious activities or first-time logins
                // For now, we'll just log it
                _logger.LogInformation("Login notification for user {UserId} from IP {IpAddress}", userId, ipAddress);

                // In a real scenario, you might want to send email notifications for:
                // - First-time logins
                // - Logins from new devices/locations
                // - Multiple failed login attempts
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process login notification for user {UserId}", userId);
            }
        }

        public async Task SendSystemAlertAsync(string message, string level = "info")
        {
            try
            {
                var adminEmails = await GetAdminEmailsAsync();
                if (!adminEmails.Any()) return;

                var levelText = level switch
                {
                    "error" => "خطأ",
                    "warning" => "تحذير",
                    "info" => "معلومات",
                    _ => "إشعار"
                };

                var levelColor = level switch
                {
                    "error" => "#dc3545",
                    "warning" => "#ffc107",
                    "info" => "#17a2b8",
                    _ => "#6c757d"
                };

                var subject = $"تنبيه النظام - {levelText}";
                var body = $@"
                    <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
                        <h3 style='color: {levelColor};'>تنبيه النظام - {levelText}</h3>
                        <p>{message}</p>
                        <p><strong>التاريخ:</strong> {DateTime.Now:yyyy/MM/dd HH:mm}</p>
                    </div>";

                await _emailService.SendEmailAsync(adminEmails, subject, body);

                _logger.LogInformation("System alert sent: {Message} (Level: {Level})", message, level);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send system alert: {Message}", message);
            }
        }

        private async Task<IEnumerable<string>> GetAdminEmailsAsync()
        {
            try
            {
                return await _context.Users
                    .Where(u => u.IsActive && u.UserRoles.Any(ur => ur.Role.Name == "Admin"))
                    .Select(u => u.Email)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get admin emails");
                return new List<string>();
            }
        }
    }
}

using ManagementSystem.Data;
using ManagementSystem.Models;
using ManagementSystem.Models.ViewModels;
using Microsoft.EntityFrameworkCore;

namespace ManagementSystem.Services
{
    public interface IAuditService
    {
        Task LogAsync(string action, string entityName, string? entityId, string? oldValues, string? newValues, string userId);
        Task<IEnumerable<AuditLogItemViewModel>> GetRecentActivitiesAsync(string? userId = null, int count = 10);
        Task<IEnumerable<AuditLogItemViewModel>> GetAuditLogsAsync(string? userId = null, string? entityName = null, DateTime? fromDate = null, DateTime? toDate = null, int pageNumber = 1, int pageSize = 50);
        Task<int> GetAuditLogsCountAsync(string? userId = null, string? entityName = null, DateTime? fromDate = null, DateTime? toDate = null);
    }

    public class AuditService : IAuditService
    {
        private readonly ApplicationDbContext _context;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AuditService(ApplicationDbContext context, IHttpContextAccessor httpContextAccessor)
        {
            _context = context;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task LogAsync(string action, string entityName, string? entityId, string? oldValues, string? newValues, string userId)
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                var ipAddress = GetClientIpAddress(httpContext);
                var userAgent = httpContext?.Request.Headers["User-Agent"].ToString();

                var auditLog = new AuditLog
                {
                    Action = action,
                    EntityName = entityName,
                    EntityId = entityId,
                    OldValues = oldValues,
                    NewValues = newValues,
                    UserId = userId,
                    IpAddress = ipAddress,
                    UserAgent = userAgent?.Length > 500 ? userAgent.Substring(0, 500) : userAgent,
                    CreatedAt = DateTime.UtcNow
                };

                _context.AuditLogs.Add(auditLog);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log the exception but don't throw to avoid breaking the main operation
                // In a real application, you would use a proper logging framework
                Console.WriteLine($"Audit logging failed: {ex.Message}");
            }
        }

        public async Task<IEnumerable<AuditLogItemViewModel>> GetRecentActivitiesAsync(string? userId = null, int count = 10)
        {
            var query = _context.AuditLogs
                .Include(a => a.User)
                .AsQueryable();

            if (!string.IsNullOrEmpty(userId))
            {
                query = query.Where(a => a.UserId == userId);
            }

            var auditLogs = await query
                .OrderByDescending(a => a.CreatedAt)
                .Take(count)
                .Select(a => new AuditLogItemViewModel
                {
                    Id = a.Id,
                    Action = a.Action,
                    EntityName = a.EntityName,
                    EntityId = a.EntityId,
                    CreatedAt = a.CreatedAt,
                    UserName = $"{a.User.FirstName} {a.User.LastName}",
                    IpAddress = a.IpAddress
                })
                .ToListAsync();

            return auditLogs;
        }

        public async Task<IEnumerable<AuditLogItemViewModel>> GetAuditLogsAsync(
            string? userId = null, 
            string? entityName = null, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            int pageNumber = 1, 
            int pageSize = 50)
        {
            var query = _context.AuditLogs
                .Include(a => a.User)
                .AsQueryable();

            if (!string.IsNullOrEmpty(userId))
            {
                query = query.Where(a => a.UserId == userId);
            }

            if (!string.IsNullOrEmpty(entityName))
            {
                query = query.Where(a => a.EntityName == entityName);
            }

            if (fromDate.HasValue)
            {
                query = query.Where(a => a.CreatedAt >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                query = query.Where(a => a.CreatedAt <= toDate.Value);
            }

            var auditLogs = await query
                .OrderByDescending(a => a.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .Select(a => new AuditLogItemViewModel
                {
                    Id = a.Id,
                    Action = a.Action,
                    EntityName = a.EntityName,
                    EntityId = a.EntityId,
                    CreatedAt = a.CreatedAt,
                    UserName = $"{a.User.FirstName} {a.User.LastName}",
                    IpAddress = a.IpAddress
                })
                .ToListAsync();

            return auditLogs;
        }

        public async Task<int> GetAuditLogsCountAsync(
            string? userId = null, 
            string? entityName = null, 
            DateTime? fromDate = null, 
            DateTime? toDate = null)
        {
            var query = _context.AuditLogs.AsQueryable();

            if (!string.IsNullOrEmpty(userId))
            {
                query = query.Where(a => a.UserId == userId);
            }

            if (!string.IsNullOrEmpty(entityName))
            {
                query = query.Where(a => a.EntityName == entityName);
            }

            if (fromDate.HasValue)
            {
                query = query.Where(a => a.CreatedAt >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                query = query.Where(a => a.CreatedAt <= toDate.Value);
            }

            return await query.CountAsync();
        }

        private string? GetClientIpAddress(HttpContext? httpContext)
        {
            if (httpContext == null) return null;

            // Check for forwarded IP first (in case of proxy/load balancer)
            var forwardedFor = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                var ips = forwardedFor.Split(',', StringSplitOptions.RemoveEmptyEntries);
                if (ips.Length > 0)
                {
                    return ips[0].Trim();
                }
            }

            // Check for real IP header
            var realIp = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
            {
                return realIp;
            }

            // Fall back to connection remote IP
            return httpContext.Connection.RemoteIpAddress?.ToString();
        }
    }
}

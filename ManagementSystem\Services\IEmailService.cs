using System.Net;
using System.Net.Mail;
using Microsoft.Extensions.Options;

namespace ManagementSystem.Services
{
    public interface IEmailService
    {
        Task SendEmailAsync(string to, string subject, string body, bool isHtml = true);
        Task SendEmailAsync(IEnumerable<string> to, string subject, string body, bool isHtml = true);
        Task SendPasswordResetEmailAsync(string to, string resetLink);
        Task SendWelcomeEmailAsync(string to, string userName, string tempPassword);
        Task SendAccountStatusChangeEmailAsync(string to, string userName, bool isActive);
    }

    public class EmailService : IEmailService
    {
        private readonly EmailSettings _emailSettings;
        private readonly ILogger<EmailService> _logger;

        public EmailService(IOptions<EmailSettings> emailSettings, ILogger<EmailService> logger)
        {
            _emailSettings = emailSettings.Value;
            _logger = logger;
        }

        public async Task SendEmailAsync(string to, string subject, string body, bool isHtml = true)
        {
            await Send<PERSON>mailAsync(new[] { to }, subject, body, isHtml);
        }

        public async Task SendEmailAsync(IEnumerable<string> to, string subject, string body, bool isHtml = true)
        {
            try
            {
                if (!_emailSettings.Enabled)
                {
                    _logger.LogInformation("Email service is disabled. Email not sent to: {Recipients}", string.Join(", ", to));
                    return;
                }

                using var client = new SmtpClient(_emailSettings.SmtpServer, _emailSettings.SmtpPort);
                client.EnableSsl = _emailSettings.EnableSsl;
                client.UseDefaultCredentials = false;
                client.Credentials = new NetworkCredential(_emailSettings.Username, _emailSettings.Password);

                var message = new MailMessage
                {
                    From = new MailAddress(_emailSettings.FromEmail, _emailSettings.FromName),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = isHtml
                };

                foreach (var recipient in to)
                {
                    message.To.Add(recipient);
                }

                await client.SendMailAsync(message);
                _logger.LogInformation("Email sent successfully to: {Recipients}", string.Join(", ", to));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email to: {Recipients}", string.Join(", ", to));
                throw;
            }
        }

        public async Task SendPasswordResetEmailAsync(string to, string resetLink)
        {
            var subject = "إعادة تعيين كلمة المرور - نظام الإدارة";
            var body = $@"
                <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
                    <h2>إعادة تعيين كلمة المرور</h2>
                    <p>تم طلب إعادة تعيين كلمة المرور لحسابك في نظام الإدارة.</p>
                    <p>للمتابعة، يرجى النقر على الرابط التالي:</p>
                    <p><a href='{resetLink}' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعادة تعيين كلمة المرور</a></p>
                    <p>إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.</p>
                    <p>هذا الرابط صالح لمدة 24 ساعة فقط.</p>
                    <hr>
                    <p style='color: #666; font-size: 12px;'>نظام الإدارة</p>
                </div>";

            await SendEmailAsync(to, subject, body);
        }

        public async Task SendWelcomeEmailAsync(string to, string userName, string tempPassword)
        {
            var subject = "مرحباً بك في نظام الإدارة";
            var body = $@"
                <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
                    <h2>مرحباً بك في نظام الإدارة</h2>
                    <p>عزيزي {userName}،</p>
                    <p>تم إنشاء حسابك بنجاح في نظام الإدارة.</p>
                    <p><strong>بيانات تسجيل الدخول:</strong></p>
                    <ul>
                        <li>البريد الإلكتروني: {to}</li>
                        <li>كلمة المرور المؤقتة: {tempPassword}</li>
                    </ul>
                    <p style='color: #d9534f;'><strong>مهم:</strong> يرجى تغيير كلمة المرور المؤقتة عند تسجيل الدخول لأول مرة.</p>
                    <p>يمكنك تسجيل الدخول من خلال الرابط التالي:</p>
                    <p><a href='#' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a></p>
                    <hr>
                    <p style='color: #666; font-size: 12px;'>نظام الإدارة</p>
                </div>";

            await SendEmailAsync(to, subject, body);
        }

        public async Task SendAccountStatusChangeEmailAsync(string to, string userName, bool isActive)
        {
            var subject = isActive ? "تم تفعيل حسابك - نظام الإدارة" : "تم تعطيل حسابك - نظام الإدارة";
            var statusText = isActive ? "تفعيل" : "تعطيل";
            var statusColor = isActive ? "#28a745" : "#dc3545";

            var body = $@"
                <div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>
                    <h2 style='color: {statusColor};'>تغيير حالة الحساب</h2>
                    <p>عزيزي {userName}،</p>
                    <p>تم {statusText} حسابك في نظام الإدارة.</p>
                    {(isActive ? 
                        "<p>يمكنك الآن تسجيل الدخول والوصول إلى النظام.</p>" : 
                        "<p>لم تعد قادراً على الوصول إلى النظام. إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع المدير.</p>")}
                    <hr>
                    <p style='color: #666; font-size: 12px;'>نظام الإدارة</p>
                </div>";

            await SendEmailAsync(to, subject, body);
        }
    }

    public class EmailSettings
    {
        public bool Enabled { get; set; } = false;
        public string SmtpServer { get; set; } = string.Empty;
        public int SmtpPort { get; set; } = 587;
        public bool EnableSsl { get; set; } = true;
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string FromEmail { get; set; } = string.Empty;
        public string FromName { get; set; } = "نظام الإدارة";
    }
}

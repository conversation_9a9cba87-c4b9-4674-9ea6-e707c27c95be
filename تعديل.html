<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام مراقبة الجمعيات التعاونية - بالدينار الكويتي</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* ...existing code... */
    </style>
</head>
<body>
    <!-- ...existing code... -->
    <script>
        // تفعيل عناصر التنقل
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // إضافة تأثيرات تفاعلية للأزرار
        document.querySelectorAll('.action-btn').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
            });
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // محاكاة تحميل البيانات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('highlight');
                    setTimeout(() => card.classList.remove('highlight'), 1500);
                }, 200 * index);
            });
            // إظهار رسالة ترحيب
            setTimeout(() => {
                const notifications = document.querySelector('.notifications');
                const welcome = document.createElement('div');
                welcome.className = 'notification success';
                welcome.innerHTML = '<div class="notification-icon"><i class="fas fa-check-circle"></i></div><div class="notification-content"><h4>مرحباً بك!</h4><p>تم تحميل النظام بنجاح.</p></div>';
                notifications.appendChild(welcome);
                setTimeout(() => welcome.remove(), 5000);
            }, 1000);
        });

        // زر العودة للأعلى
        const backToTopBtn = document.querySelector('.back-to-top');
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        });
        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({top: 0, behavior: 'smooth'});
        });

        // الرسوم البيانية
        document.addEventListener('DOMContentLoaded', function() {
            // رسم مبيعات السوق المركزي
            const salesBox = document.querySelectorAll('.chart-box')[0];
            const salesCanvas = document.createElement('canvas');
            salesCanvas.id = 'salesChart';
            salesBox.appendChild(salesCanvas);
            const salesChart = new Chart(salesCanvas.getContext('2d'), {
                type: 'bar',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'المبيعات',
                        data: [12000, 15000, 11000, 18000, 14000, 17000],
                        backgroundColor: '#2c5f2d',
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {legend: {display: false}},
                    scales: {y: {beginAtZero: true}}
                }
            });
            // رسم المخالفات
            const violationsBox = document.querySelectorAll('.chart-box')[1];
            const violationsCanvas = document.createElement('canvas');
            violationsCanvas.id = 'violationsChart';
            violationsBox.appendChild(violationsCanvas);
            const violationsChart = new Chart(violationsCanvas.getContext('2d'), {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'المخالفات',
                        data: [2, 3, 1, 4, 2, 6],
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231,76,60,0.1)',
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {legend: {display: false}},
                    scales: {y: {beginAtZero: true}}
                }
            });
        });

        // فرز الجداول
        document.querySelectorAll('th').forEach(th => {
            th.addEventListener('click', () => {
                th.classList.toggle('desc');
            });
        });

        // تمييز الصفوف عند المرور عليها
        document.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('mouseenter', () => {
                row.style.backgroundColor = 'rgba(151, 188, 98, 0.05)';
            });
            row.addEventListener('mouseleave', () => {
                row.style.backgroundColor = '';
            });
            // إضافة إمكانية النقر على الصف
            row.addEventListener('click', () => {
                row.classList.add('highlight');
                setTimeout(() => row.classList.remove('highlight'), 3000);
            });
        });
    </script>
</body>
</html>
